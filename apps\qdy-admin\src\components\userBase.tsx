import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { cn } from '@/lib/utils';
import { AccountTypeBadge } from './accountTypeBadge';
import { PlatformBadge } from './platformBadge';

export interface AccountUser {
  id: string | number;
  name: string;
  avatar: string;
  accountRole?: string;
  platform?: number;
}

function UserBase({
  onItemClick,
  size,
  isExpired,
  ...props
}: AccountUser & {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  isExpired?: boolean;
  onItemClick?: (platformId: number, name: string) => void;
}) {
  return (
    <div
      className="flex items-center gap-2 cursor-pointer"
      onClick={() => {
        if (typeof onItemClick === 'function') {
          onItemClick(props.platform ?? 0, props.name);
        }
      }}
    >
      <div className="flex-shrink-0 relative">
        <Avatar
          className={cn(
            'w-7 h-7',
            size === 'sm' && 'w-4 h-4',
            size === 'md' && 'w-7 h-7',
            size === 'lg' && 'w-8 h-8',
            size === 'xl' && 'w-10 h-10',
            size === '2xl' && 'w-16 h-16'
          )}
        >
          <AvatarImage src={props.avatar} />
          <AvatarFallback className="text-xs">
            {props.name?.substring(0, 2)}
          </AvatarFallback>
        </Avatar>
        {props.platform !== undefined && (
          <PlatformBadge value={props.platform} />
        )}
      </div>
      <div>
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          <span
            className={cn('flex-grow-0 flex-shrink-1 line-clamp-1')}
            style={{ maxWidth: 'clac(100% - 20px)' }}
          >
            {props.name}
          </span>
          {props.accountRole && <AccountTypeBadge type={'blue'} />}
        </div>
        {isExpired && (
          <div className="text-destructive text-xs">授权已失效</div>
        )}
      </div>
    </div>
  );
}

export default UserBase;
