import { Button } from '@mono/ui/button';
import { X } from 'lucide-react';
import { ComponentProps } from 'react';
import { cn } from '@/lib/utils';

export function Xbutton({
  className,
  ...porps
}: ComponentProps<typeof Button>) {
  return (
    <Button
      type="button"
      className={cn(
        'h-4 w-4 rounded-full text-muted-foreground absolute -top-1 -right-1',
        className
      )}
      variant="secondary"
      size="icon"
      {...porps}
    >
      <X className="h-3 w-3" />
    </Button>
  );
}
