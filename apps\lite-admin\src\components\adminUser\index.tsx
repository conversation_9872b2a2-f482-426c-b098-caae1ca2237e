import { DataTable } from '@/components/dataTable';
import { useCallback, useMemo, useRef, useState } from 'react';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { CreateAdmin, EditorAdmin } from './CreateAdmin';
import { Button } from '@mono/ui/button';
import {
  deleteAdmin,
  getAdminList,
  resetPassword,
  updateUserStatus,
} from '@/api/admin.ts';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Account } from '@/types/admins';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { alertBaseManager } from '../alertBase';
import { Badge } from '@mono/ui/badge';
import { Switch } from '@mono/ui/switch';
import { formatDate } from '@mono/utils/day';
import { useDialog } from '@mono/ui/common/DialogProvider';
import { PasswordInput } from '../PasswordInput';
import { toast } from 'sonner';
import { PhotoView, PhotoProvider } from 'react-photo-view';

// 获取身份dom
function getRoleDom(role: Account['role']) {
  switch (role) {
    case 0:
      return <Badge variant="default">超级管理员</Badge>;
    case 1:
      return <Badge variant="secondary">管理员</Badge>;
    default:
      return <Badge variant="outline">客服</Badge>;
  }
}

export const AdminUserPage = () => {
  const queryClient = useQueryClient();
  const [editorOpen, setEditorOpen] = useState(false);
  const [editorId, setEditorId] = useState<string>('');
  const dialogHandler = useDialog();
  const newPassword = useRef('');

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const query = useQuery({
    queryKey: ['adminList', pagination],
    queryFn: () =>
      getAdminList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  // 更新用户状态
  const statusMutation = useMutation({
    mutationFn: updateUserStatus,
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        ['adminList', pagination],
        (oldData: typeof query.data) => {
          return {
            ...oldData,
            data: oldData!.data.map((item: Account) => {
              if (item.id === variables.id) {
                return {
                  ...item,
                  status: variables.status,
                };
              }
              return item;
            }),
          };
        }
      );
    },
  });
  // 重置密码
  const resetPasswordMutation = useMutation({
    mutationFn: resetPassword,
    onSuccess: () => {
      toast.success('重置密码成功');
    },
  });

  const handleEditorClick = (id: string) => {
    setEditorId(id);
    setEditorOpen(true);
  };
  const handelDelete = useCallback(
    async (id: string) => {
      await deleteAdmin(id);
      query.refetch();
    },
    [query]
  );

  const columns = useMemo<Array<ColumnDef<Account>>>(() => {
    return [
      {
        header: '姓名',
        accessor: 'name',
        cell: ({ row }) => {
          return <span>{row.original.name}</span>;
        },
      },
      {
        header: '账号',
        accessor: 'username',
        cell: ({ row }) => {
          return <span>{row.original.username}</span>;
        },
      },
      {
        header: '身份',
        accessor: 'role',
        cell: ({ row }) => {
          return getRoleDom(row.original.role);
        },
      },
      {
        header: '二维码',
        accessor: 'qrCode',
        cell: ({ row }) => {
          return row.original.qrCode ?
              <PhotoProvider>
                <PhotoView src={row.original.qrCode}>
                  <img src={row.original.qrCode} className="w-10 h-10" alt="" />
                </PhotoView>
              </PhotoProvider>
            : <span>-</span>;
        },
      },
      {
        header: '状态',
        accessor: 'status',
        cell: ({ row }) => {
          const hasService = row.original.role === 2;
          return (
            hasService && (
              <Switch
                onCheckedChange={(checked) =>
                  statusMutation.mutate({
                    id: row.original.id,
                    status: checked ? 1 : 0,
                  })
                }
                checked={row.original.status === 1}
              />
            )
          );
        },
      },
      {
        header: '创建时间',
        accessor: 'createdAt',
        cell: ({ row }) => {
          return <span>{formatDate(row.original.createdAt)}</span>;
        },
      },

      {
        header: '操作',
        accessor: 'actions',
        cell: ({ row }) => (
          <DropdownMenuComponent>
            <DropdownMenuItem
              onClick={() => handleEditorClick(row.original.id)}
            >
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                dialogHandler.open({
                  title: '重置密码',
                  description: '请输入新密码',
                  children: (
                    <PasswordInput
                      onChange={(v) =>
                        (newPassword.current = v.currentTarget.value)
                      }
                    />
                  ),
                  onSubmit: () => {
                    if (!newPassword.current) {
                      toast.error('请输入正确的新密码');
                      return;
                    }
                    resetPasswordMutation.mutate({
                      id: row.original.id,
                      password: newPassword.current,
                    });
                  },
                });
              }}
            >
              重置密码
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                alertBaseManager.open({
                  title: '删除账号',
                  description: '确定删除该账号吗？',
                  onSubmit: () => {
                    handelDelete(row.original.id);
                  },
                });
              }}
              className="text-destructive focus:text-destructive"
            >
              删除
            </DropdownMenuItem>
          </DropdownMenuComponent>
        ),
      },
    ];
  }, [dialogHandler, handelDelete, resetPasswordMutation, statusMutation]);

  const [open, setOpen] = useState(false);

  const refresh = async () => {
    query.refetch();
  };

  return (
    <div className="flex flex-col w-full h-full gap-2 overflow-hidden">
      <div>
        <Button onClick={() => setOpen(true)}>新增账号</Button>
      </div>
      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
      <CreateAdmin open={open} setOpen={setOpen} onSuccess={refresh} />
      <EditorAdmin
        itemId={editorId}
        open={editorOpen}
        setOpen={setEditorOpen}
        onSuccess={refresh}
      />
    </div>
  );
};
