import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { OpenPlatformAppParams } from '@/types/openPlatform';
import { Search, RotateCcw } from 'lucide-react';

interface AppSearchProps {
  onSearch: (params: Omit<OpenPlatformAppParams, 'userId'>) => void;
  values: Partial<Omit<OpenPlatformAppParams, 'userId'>>;
}

type SearchForm = {
  appName?: string;
  appType?: string;
};

export function AppSearch({ onSearch, values }: AppSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      appName: values.appName || '',
      appType: values.appType || '',
    },
  });

  const handleSubmit = (data: SearchForm) => {
    const params: Omit<OpenPlatformAppParams, 'userId'> = {
      appName: data.appName || undefined,
      appType: data.appType || undefined,
    };
    onSearch(params);
  };

  const handleReset = () => {
    form.reset({
      appName: '',
      appType: '',
    });
    onSearch({});
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="appName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>应用名称</FormLabel>
                <Input {...field} placeholder="请输入应用名称" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>应用类型</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择应用类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部</SelectItem>
                    <SelectItem value="web">Web应用</SelectItem>
                    <SelectItem value="mobile">移动应用</SelectItem>
                    <SelectItem value="api">API应用</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <div className="flex items-end gap-2">
            <Button type="submit" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              搜索
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="w-4 h-4" />
              重置
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
