import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { omitBy } from 'lodash';
import { TeamsReq } from '@/types/team';
import { SelectService } from '../SelectService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { useEffect } from 'react';
import { salesTypeMap } from '../../constants/salesTypeMap.ts';
import { SelectApplication } from '../SelectApplication.tsx';

type SearchForm = Omit<
  TeamsReq,
  'startTime' | 'endTime' | 'expiredEndTime' | 'expiredStartTime'
> & {
  createDateRange?: {
    from: Date;
    to: Date;
  };
  expiredDateRange?: {
    from: Date;
    to: Date;
  };
};

export function TableSearch({
  values,
  onSearch,
}: {
  values?: TeamsReq;
  onSearch: (values: TeamsReq) => void;
}) {
  const { setValue, ...form } = useForm<SearchForm>({
    defaultValues: {
      customerId: values?.customerId?.trim() ?? '',
      phone: values?.phone?.trim() ?? '',
      teamName: values?.teamName?.trim() ?? '',
      salesType: values?.salesType ?? -1,
    },
  });

  useEffect(() => {
    if (values?.expiredStartTime && values.expiredEndTime) {
      setValue('expiredDateRange', {
        from: new Date(values.expiredStartTime),
        to: new Date(values.expiredEndTime),
      });
    }
  }, [setValue, values]);

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: TeamsReq = {
      phone: values.phone?.trim(),
      startTime: values?.createDateRange?.from?.getTime(),
      endTime: values.createDateRange?.to?.getTime(),
      expiredStartTime: values.expiredDateRange?.from?.getTime(),
      expiredEndTime: values.expiredDateRange?.to?.getTime(),
      customerId: values.customerId?.trim(),
      teamName: values.teamName?.trim(),
      isVip: values.isVip,
      salesType:
        values?.salesType !== undefined && values.salesType >= 0 ?
          values.salesType
        : undefined,
      isOpenPlatform:
        values.isOpenPlatform === 'all' ?
          undefined
        : values.isOpenPlatform?.trim(),
      applicationId: values.applicationId?.trim(),
    };
    const paramsOmitEmpty = omitBy(
      params,
      (value) => value === undefined || value === null || value === ''
    );
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form setValue={setValue} {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队名称</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索团队名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="expiredDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>VIP到期时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isVip"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>VIP</FormLabel>
              <Select
                onValueChange={(value) =>
                  field.onChange(value === 'all' ? undefined : value === 'vip')
                }
                defaultValue={
                  field.value === undefined ? 'all'
                  : field.value ?
                    'vip'
                  : 'unVip'
                }
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {/* VIP */}
                  <SelectItem value="vip">已开通</SelectItem>
                  {/* 非VIP */}
                  <SelectItem value="unVip">未开通</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>客服</FormLabel>
              <FormControl>
                <SelectService
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="salesType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="shrink-0">销售类型</FormLabel>
              <Select
                onValueChange={(value) => field.onChange(parseInt(value))}
                defaultValue={field.value?.toString()}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.keys(salesTypeMap).map((key) => (
                    <SelectItem key={key} value={key}>
                      {salesTypeMap[key as keyof typeof salesTypeMap]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isOpenPlatform"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="shrink-0">开放平台</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="true">是</SelectItem>
                  <SelectItem value="false">否</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="applicationId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>应用名称</FormLabel>
              <FormControl>
                <SelectApplication
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
