import {
  CreditCard,
  ShoppingCart,
  Users2,
  Megaphone,
  SquareGanttChart,
  Rss,
  CircleUserRound,
  Smartphone,
  Users,
  LineChart,
  MessageCircle,
  // MessageCircle,
} from 'lucide-react';

// 导航数据 用户管理 ｜ 优惠券 ｜ 通知公告 ｜ 订单
export const navs = [
  {
    name: '用户管理',
    icon: <CircleUserRound className="h-5 w-5" />,
    path: '/user',
  },
  {
    name: '团队管理',
    icon: <Users2 className="h-5 w-5" />,
    path: '/team',
  },
  {
    name: '账号列表',
    icon: <Users className="h-5 w-5" />,
    path: '/account',
  },
  {
    name: '订单',
    icon: <ShoppingCart className="h-5 w-5" />,
    path: '/order',
  },
  {
    name: '数据',
    icon: <SquareGanttChart className="h-5 w-5" />,
    path: '/overview',
  },
  {
    name: '业绩统计',
    icon: <LineChart className="h-5 w-5" />,
    path: '/performance-statistics',
  },
  {
    name: '视频号统计',
    icon: <MessageCircle className="h-5 w-5" />,
    path: '/wechat-statistics',
  },
  // {
  //   name: 'IP统计',
  //   icon: <LineChart className="h-5 w-5" />,
  //   path: '/ip-statistics',
  // },
  {
    name: '优惠券',
    icon: <CreditCard className="h-5 w-5" />,
    path: '/coupon',
  },
  {
    name: '通知公告',
    icon: <Megaphone className="h-5 w-5" />,
    path: '/notice',
  },
  {
    name: '渠道',
    icon: <Rss className="h-5 w-5" />,
    path: '/channel',
  },
  {
    name: 'App版本',
    icon: <Smartphone className="h-5 w-5" />,
    path: '/app',
  },
];
