import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { omitBy } from 'lodash';
import { MemberReq } from '@/types/user';
import { SelectService } from '../SelectService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { SelectApplication } from '../SelectApplication';

type SearchForm = Omit<
  MemberReq,
  'createStartTime' | 'createEndTime' | 'lastUseStartTime' | 'lastUseEndTime'
> & {
  createDateRange?: {
    from: Date;
    to: Date;
  };
  lastDateRange?: {
    from: Date;
    to: Date;
  };
};

export function TableSearch({
  values,
  onSearch,
}: {
  values?: MemberReq;
  onSearch: (values: MemberReq) => void;
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      customerId: values?.customerId?.trim() ?? '',
      phone: values?.phone?.trim() ?? '',
      channelCode: values?.channelCode?.trim() ?? '',
    },
  });

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: MemberReq = {
      phone: values.phone?.trim(),
      createStartTime: values?.createDateRange?.from?.getTime(),
      createEndTime: values.createDateRange?.to?.getTime(),
      lastUseStartTime: values.lastDateRange?.from?.getTime(),
      lastUseEndTime: values.lastDateRange?.to?.getTime(),
      customerId: values.customerId?.trim(),
      channelCode: values.channelCode?.trim(),
      applicationId: values.applicationId?.trim(),
      isOpenPlatform:
        values.isOpenPlatform === 'all' ?
          undefined
        : values.isOpenPlatform?.trim(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="lastDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>最近使用</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>客服</FormLabel>
              <FormControl>
                <SelectService
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="channelCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索渠道码" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isOpenPlatform"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="shrink-0">开放平台</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="true">是</SelectItem>
                  <SelectItem value="false">否</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="applicationId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>应用名称</FormLabel>
              <FormControl>
                <SelectApplication
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
