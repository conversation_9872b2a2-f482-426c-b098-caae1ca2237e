import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from './ui/avatar';

interface AvatarCardProps {
  src?: string;
  title: string;
  subtitle?: string;
}

export const AvatarCard: React.FC<AvatarCardProps> = ({
  src,
  title,
  subtitle,
}) => {
  return (
    <div className="flex items-center">
      <Avatar>
        {src ?
          <AvatarImage src={src} alt={title} />
        : <AvatarFallback>
            {title && title.toUpperCase().substring(0, 2)}
          </AvatarFallback>
        }
      </Avatar>
      <div className="flex flex-col ml-2 items-start gap-1 justify-center">
        <p className="font-medium line-clamp-1">{title}</p>
        {subtitle && (
          <p className="text-muted-foreground text-xs line-clamp-1">
            {subtitle}
          </p>
        )}
      </div>
    </div>
  );
};

export default AvatarCard;
