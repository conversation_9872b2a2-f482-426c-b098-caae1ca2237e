import { getIncomeDau } from '@/api/overview';
import { useQuery } from '@tanstack/react-query';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { formatBy, formatMonthDay } from '@/lib/utils/day';

const chartConfig = {
  income: {
    label: '收入',
    color: 'hsl(var(--chart-1))',
  },
  expense: {
    label: '支出',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig;

export function IncomeChart({ days }: { days: string }) {
  const { data } = useQuery({
    queryKey: ['getIncomeDau'],
    queryFn: () => getIncomeDau(parseInt(days)),
  });

  return (
    <Card className="h-full overflow-hidden flex flex-col flex-1">
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0">
        <div className="flex flex-shrink-0 flex-col justify-center gap-1 px-6 py-4">
          <CardTitle>30日收入趋势</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-2 flex-1 h-0">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
              top: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => formatMonthDay(value)}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => formatBy(value, 'YYYY-MM-DD')}
                />
              }
            />

            <defs>
              <linearGradient id="fillIncome" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-income)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-income)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillExpense" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-expense)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-expense)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>

            <Area
              dataKey="income"
              type="natural"
              fill="url(#fillIncome)"
              fillOpacity={0.4}
              stroke="var(--color-income)"
              stackId="incomeExpense"
            />
            <Area
              dataKey="expense"
              type="natural"
              fill="url(#fillExpense)"
              fillOpacity={0.4}
              stroke="var(--color-expense)"
              stackId="incomeExpense"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
