import { createFileRoute, useRouter } from '@tanstack/react-router';
import { z } from 'zod';
import { useCallback } from 'react';
import { LoginContainer } from '@/components/login/loginContainer';
import { UserLogin } from '@/types/user';

export const Route = createFileRoute('/login')({
  validateSearch: z.object({
    redirect: z.string().optional(),
  }),
  component: LoginComponent,
});

function LoginComponent() {
  const router = useRouter();
  const search = Route.useSearch();

  const loginNext = useCallback((userLogin: UserLogin) => {
    console.log(`userLogin:\n${userLogin}`);
    if (userLogin) {
      const mainRoute = search.redirect ?? '/';
      router.history.replace(mainRoute);
    }
  }, []);

  return (
    <main className="grid h-screen place-items-center">
      <LoginContainer loginNext={loginNext} />
    </main>
  );
}
