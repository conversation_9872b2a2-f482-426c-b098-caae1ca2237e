import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import {
  createRef,
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { CircleAlert } from 'lucide-react';
import { Button } from '@mono/ui/button';

type AlertBaseHandle = {
  open: (options: {
    title: string;
    description?: string;
    okText?: string;
    cancelText?: string;
    isHideIcon?: boolean;
    onSubmit?: () => void;
    onCancel?: () => void;
  }) => void;
};

const alertRef = createRef<AlertBaseHandle>();

export const AlertBaseWrapper = () => {
  return <AlertBase ref={alertRef} />;
};

export const alertBaseManager = {
  open(options: Parameters<AlertBaseHandle['open']>[0]) {
    alertRef.current?.open(options);
  },
};

export const AlertBase = forwardRef<AlertBaseHandle, NonNullable<unknown>>(
  (_, ref) => {
    const [open, setOpen] = useState(false);
    const options = useRef<Parameters<AlertBaseHandle['open']>[0]>();
    useImperativeHandle(ref, () => ({
      open(option) {
        options.current = option;
        setOpen(true);
      },
    }));
    return (
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-1 text-base font-medium">
              {!options.current?.isHideIcon && (
                <CircleAlert className="text-[#faad14] h-5 w-5" />
              )}
              {options.current?.title}
            </AlertDialogTitle>
            {options.current?.description && (
              <AlertDialogDescription>
                {options.current?.description}
              </AlertDialogDescription>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={options.current?.onCancel}>
              {options.current?.cancelText ?? '取消'}
            </AlertDialogCancel>
            <Button
              variant="destructive"
              onClick={() => {
                options.current?.onSubmit?.();
                setOpen(false);
              }}
            >
              {options.current?.okText ?? '确定'}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }
);
