import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { AccountParams } from '@/types/account';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@mono/ui/select';
import { platformMap } from '@/lib/platform';

const formSchema = z.object({
  platform: z.string().optional(),
  name: z.string().optional(),
  status: z.string().optional(),
  createTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  invitationCode: z.string().optional(),
});

export function AccountTableSearch({
  values,
  onSearch,
}: {
  values: AccountParams;
  onSearch: (values: AccountParams) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...values,
      platform: values.platform ? values.platform.toString() : undefined,
    },
  });

  function onSubmit() {
    const values: z.infer<typeof formSchema> = form.getValues();
    const params: AccountParams = {
      platform:
        values.platform && values.platform !== 'all' ?
          Number(values.platform)
        : undefined,
      name: values.name,
      status:
        values.status === 'all' || !values.status ? undefined
        : values.status === 'normal' ? 'normal'
        : 'abnormal',
      startTime: values.createTimeRange?.from?.getTime(),
      endTime: values.createTimeRange?.to?.getTime(),
      invitationCode: values.invitationCode?.trim(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => {
      return value === '' || value === null || value === undefined;
    });
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form className="flex flex-wrap gap-4 justify-start p-0.5">
        <FormField
          control={form.control}
          name="platform"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="flex-shrink-0">平台</FormLabel>
              <FormControl>
                <Select
                  onValueChange={(value: string) => {
                    field.onChange(value);
                    onSubmit();
                  }}
                  defaultValue={field.value}
                >
                  <SelectTrigger className="w-28">
                    <SelectValue placeholder="请选择平台" />
                  </SelectTrigger>
                  <SelectContent>
                    {platformMap.map((item) => (
                      <SelectItem value={item.key} key={item.key}>
                        {item.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">账号名称</FormLabel>
              <Input
                className="w-40"
                placeholder="请输入账号名"
                onChange={(e) => {
                  field.onChange(e.target?.value);
                  onSubmit();
                }}
              />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="flex-shrink-0">状态</FormLabel>
              <FormControl>
                <Select
                  onValueChange={(value: string) => {
                    field.onChange(value);
                    onSubmit();
                  }}
                  defaultValue={field.value}
                >
                  <SelectTrigger className="w-28">
                    <SelectValue placeholder="请选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="normal">正常</SelectItem>
                    <SelectItem value="abnormal">异常</SelectItem>
                    {/* <SelectItem value="freeze">冻结</SelectItem> */}
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="createTimeRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">添加时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={(value: { from?: Date; to?: Date } | undefined) => {
                  field.onChange(value);
                  onSubmit();
                }}
              ></CalendarRange>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="invitationCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队ID</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入团队ID筛选"
                  onChange={(e) => {
                    field.onChange(e.target?.value);
                    onSubmit();
                  }}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
