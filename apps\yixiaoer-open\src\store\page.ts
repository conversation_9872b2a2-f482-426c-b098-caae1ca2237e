import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type PageState = {
  applicationId: string | undefined;
};

type PageAction = {
  setApplicationId: (applicationId: string) => void;
};

export const usePageStore = create<PageState & PageAction>()(
  immer((set) => ({
    applicationId: undefined,
    setApplicationId: (applicationId) => set({ applicationId }),
  }))
);
