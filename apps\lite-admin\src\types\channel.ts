import { zodIsPassword } from '@/lib/validate';
import { z } from 'zod';

export type Channel = {
  /**
   * 渠道码
   */
  channelCode: string;
  /**
   * 渠道名称
   */
  channelName: string;
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 上架状态
   */
  enabled: boolean;
  /**
   * 渠道ID
   */
  id: string;
  /**
   * 订单数
   */
  orderCount: number;
  /**
   * 下单总金额数
   */
  orderTotalAmount: number;
  /**
   * 用户数
   */
  userCount: number;
};

export type ChannelReq = {
  /**
   * 渠道码
   */
  channelCode?: string;
  /**
   * 渠道名称
   */
  channelName?: string;
};

export interface ChannelDetail {
  /**
   * 渠道码
   */
  channelCode: string;
  /**
   * 渠道名称
   */
  channelName: string;
  /**
   * 创建时间
   */
  createdAt?: number;
  /**
   * 上架状态
   */
  enabled: boolean;
  /**
   * 赠送天数
   */
  giftDays?: number;
  /**
   * 渠道ID
   */
  id: string;
  /**
   * 渠道账号
   */
  username: string;
}

export const channelformSchema = z.object({
  channelName: z
    .string()
    .min(1, '渠道名称不能为空')
    .max(20, '渠道名称不能超过20个字符'),
  channelCode: z.string().length(6, '渠道码必须为6位字符'),
  username: z.string().min(1, '账号不能为空').max(20, '账号不能超过20个字符'),
  password: zodIsPassword,
  giftDays: z.number().min(0, '赠送天数不能小于0天').optional(),
});

export type ChannelFormType = z.infer<typeof channelformSchema>;
