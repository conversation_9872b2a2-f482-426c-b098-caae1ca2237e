import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import {
  OpenPlatformApp,
  appStatusMap,
  appStatusStyleMap,
} from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye, Settings, Coins } from 'lucide-react';

interface AppColumnsProps {
  onViewData: (app: OpenPlatformApp) => void;
  onSetPrice: (app: OpenPlatformApp) => void;
  onViewOrders: (app: OpenPlatformApp) => void;
  onAudit: (app: OpenPlatformApp) => void;
}

export function getAppColumns({
  onViewData,
  onSetPrice,
  onViewOrders,
  onAudit,
}: AppColumnsProps): ColumnDef<OpenPlatformApp>[] {
  return [
    {
      accessorKey: 'name',
      header: '应用名称',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      accessorKey: 'appId',
      header: 'AppID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('appId')}</div>
      ),
    },
    {
      accessorKey: 'totalAccountCount',
      header: '账号总数',
      cell: ({ row }) => {
        const count = row.getValue('totalAccountCount') as number;
        return (
          <div className="text-sm font-medium">{count.toLocaleString()}</div>
        );
      },
    },
    {
      accessorKey: 'userCount',
      header: '用户数量',
      cell: ({ row }) => {
        const count = row.getValue('userCount') as number;
        return (
          <div className="text-sm font-medium">{count.toLocaleString()}</div>
        );
      },
    },
    {
      accessorKey: 'teamCount',
      header: '团队数量',
      cell: ({ row }) => {
        const count = row.getValue('teamCount') as number;
        return (
          <div className="text-sm font-medium">{count.toLocaleString()}</div>
        );
      },
    },
    {
      accessorKey: 'availableBalance',
      header: '蚁币',
      cell: ({ row }) => {
        const app = row.original;
        const balance = row.getValue('availableBalance') as number;

        return (
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-orange-600 hover:text-orange-800 flex items-center gap-1"
            onClick={() => onViewOrders(app)}
          >
            <Coins className="w-4 h-4" />
            {balance.toLocaleString()}
          </Button>
        );
      },
    },
    {
      accessorKey: 'totalTrafficMB',
      header: '流量使用量',
      cell: ({ row }) => {
        const { usedTrafficMB, totalTrafficMB } = row.original;
        // const usageMB = row.getValue('totalTrafficMB') as number;

        // 格式化流量显示
        const formatTraffic = (mb: number) => {
          if (mb >= 1024) {
            return `${(mb / 1024).toFixed(2)} GB`;
          }
          return `${mb.toFixed(2)} MB`;
        };

        return (
          <div className="text-sm font-medium">
            {formatTraffic(usedTrafficMB)}/{formatTraffic(totalTrafficMB)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as keyof typeof appStatusMap;
        const app = row.original;
        return (
          <div className="flex items-center gap-2">
            <Badge className={appStatusStyleMap[status]}>
              {appStatusMap[status]}
            </Badge>
            {status === 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAudit(app)}
                className="flex items-center gap-1"
              >
                审核
              </Button>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => {
        const timestamp = row.getValue('createdAt') as number;
        return (
          <div className="text-sm">
            {timestamp ?
              format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')
            : '-'}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const app = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewData(app)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              数据
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSetPrice(app)}
              className="flex items-center gap-1"
            >
              <Settings className="w-4 h-4" />
              设置价格
            </Button>
          </div>
        );
      },
    },
  ];
}
