import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import { OpenPlatformApp } from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye, Settings, Coins } from 'lucide-react';

interface AppColumnsProps {
  onViewData: (app: OpenPlatformApp) => void;
  onSetPrice: (app: OpenPlatformApp) => void;
  onViewOrders: (app: OpenPlatformApp) => void;
}

export function getAppColumns({
  onViewData,
  onSetPrice,
  onViewOrders,
}: AppColumnsProps): ColumnDef<OpenPlatformApp>[] {
  return [
    {
      accessorKey: 'name',
      header: '应用名称',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      accessorKey: 'appId',
      header: 'AppID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('appId')}</div>
      ),
    },
    {
      accessorKey: 'type',
      header: '应用类型',
      cell: ({ row }) => {
        const type = row.getValue('type') as string;
        const typeMap: Record<string, string> = {
          web: 'Web应用',
          mobile: '移动应用',
          api: 'API应用',
          other: '其他',
        };
        return (
          <Badge variant="outline">
            {typeMap[type] || type}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'antCoinBalance',
      header: '蚁币',
      cell: ({ row }) => {
        const app = row.original;
        const balance = row.getValue('antCoinBalance') as number;
        
        return (
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-orange-600 hover:text-orange-800 flex items-center gap-1"
            onClick={() => onViewOrders(app)}
          >
            <Coins className="w-4 h-4" />
            {balance.toLocaleString()}
          </Button>
        );
      },
    },
    {
      accessorKey: 'accountData',
      header: '账号',
      cell: ({ row }) => {
        const data = row.getValue('accountData') as number;
        return (
          <div className="text-sm font-medium">
            {data.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'trafficData',
      header: '流量',
      cell: ({ row }) => {
        const data = row.getValue('trafficData') as number;
        return (
          <div className="text-sm font-medium">
            {data.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'userData',
      header: '用户',
      cell: ({ row }) => {
        const data = row.getValue('userData') as number;
        return (
          <div className="text-sm font-medium">
            {data.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'teamInfo',
      header: '团队',
      cell: ({ row }) => {
        const teamInfo = row.getValue('teamInfo') as string;
        return (
          <div className="text-sm">
            {teamInfo || '-'}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const app = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewData(app)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              数据
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSetPrice(app)}
              className="flex items-center gap-1"
            >
              <Settings className="w-4 h-4" />
              设置价格
            </Button>
          </div>
        );
      },
    },
  ];
}
