import { But<PERSON> } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import { convertObjectToNumber } from '@/lib/utils/type';
import {
  createOrder,
  getInterest,
  giftOrder,
  orderPrice,
  renewOrder,
  upgradeOrder,
} from '@/api/order';
import { LoadingButton } from '../loadingButton';
import { SelectTeam } from '@/components/order/SelectTeam.tsx';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { Textarea } from '@mono/ui/textarea';
import { formatPrice } from '@/lib/utils';
import { TeamOrderStatus } from '@/types/team';
import { Row } from './detail';
import { addDuration, formatYearMonthDay, isExpiredFun } from '@/lib/utils/day';
import { cloneDeep } from 'lodash';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import { OrderType } from '@/types/order';
import { Loading } from '../loading';
import { StepInput } from '../stepInput';
import { HelpTooltop } from '../helpTooltop';
import { toast } from 'sonner';

// 账号数 ｜ 成员数 ｜ 时长 | 付款金额 | 私信额度（含自动回复） | 评论额度（含自动回复) | 粉丝群额度（含自动回复）
const formSchema = z.object({
  interestCount: z.number().min(1, { message: '请输入权益包数量' }),
  interestId: z.string().min(1, { message: '请选择权益' }),
  month: z.string().optional(),
  payAmount: z.string(),
  teamId: z.string().min(1, { message: '请选择团队' }),
  isPay: z.string().optional(), // z.boolean().default(true)
  remark: z.string().optional(),
  giftDays: z.number().min(0),
  days: z.string().min(1),
});

// 时长: 自定义
const customDurationValue = 'customize';

export function CreateOrder() {
  const [open, setOpen] = useState(false);
  const [currentTeam, setCurrentTeam] = useState<TeamOrderStatus | undefined>();
  const queryClient = useQueryClient();
  const [orderType, setOrderType] = useState<OrderType>('create');
  const vipQuery = useQuery({
    queryFn: () =>
      getInterest().then((res) => {
        form.setValue('interestId', `${res.interests[0].id}`);
        return res;
      }),
    queryKey: ['getInterest'],
    enabled: open,
  });

  // const vipInterest = useMemo(() => {
  //   return vipQuery.data?.interests || [];
  // }, [vipQuery.data]);

  const vipOften = useMemo(() => {
    return vipQuery.data?.vipOften || [];
  }, [vipQuery.data]);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      teamId: '',
      interestCount: 1,
      payAmount: '',
      isPay: '', // true
      giftDays: 0,
      days: '1',
    },
  });

  const handleSuccess = () => {
    queryClient.refetchQueries({ queryKey: ['orderList'] });
    setOpen(false);
    form.reset();
  };

  const mutation = useMutation({
    mutationFn: createOrder,
    onSuccess: () => {
      handleSuccess();
    },
  });

  const reNewMutation = useMutation({
    mutationFn: renewOrder,
    onSuccess: () => {
      handleSuccess();
    },
  });

  const upgradeMutation = useMutation({
    mutationFn: upgradeOrder,
    onSuccess: () => {
      handleSuccess();
    },
  });

  const giftMutation = useMutation({
    mutationFn: giftOrder,
    onSuccess: () => {
      handleSuccess();
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    // console.log(values);
    const params = cloneDeep(values);
    if (!params.remark || params.remark === '') {
      delete params.remark;
    }
    const { isPay, remark, ...rest } = params;
    if (['renew', 'create'].includes(orderType) && !rest.month) {
      toast.error('请选择时长');
      return;
    }
    if (!isPay) {
      toast.error('请选择是否赠送');
      return;
    }
    if (needPay && !rest.payAmount) {
      toast.error('请设置折扣价');
      return;
    } else if (needPay && Number(rest.payAmount) < 1) {
      toast.error('折扣价必须大于等于1元');
      return;
    }
    if (isCustomize) {
      rest.month = '0';
    } else {
      rest.days = '0';
    }
    // console.log("%c rest:\n", "color:#FF7A45", rest);
    const data = convertObjectToNumber(rest);
    switch (orderType) {
      case 'create':
        mutation.mutate({
          isPay: isPay !== '1',
          remark,
          interestId: data.interestId,
          interestCount: data.interestCount,
          teamId: data.teamId,
          month: data.month as unknown as number,
          payAmount: data.payAmount,
          days: data.days,
        });
        break;
      case 'renew':
        reNewMutation.mutate({
          isPay: isPay !== '1',
          interestId: data.interestId,
          month: data.month as unknown as number,
          teamId: data.teamId,
          payAmount: data.payAmount,
          remark: remark,
          days: data.days,
        });
        break;
      case 'upgrade':
        upgradeMutation.mutate({
          isPay: isPay !== '1',
          interestId: data.interestId,
          interestCount: data.interestCount,
          teamId: data.teamId,
          payAmount: data.payAmount,
          remark: remark,
        });
        break;
      case 'gift':
        giftMutation.mutate({
          giftDays: data.giftDays,
          interestId: data.interestId,
          teamId: data.teamId,
          remark: remark,
        });
        break;
    }
  }

  const needPay = form.watch('isPay') === '0';

  const [interestId, month, interestCount] = form.watch([
    'interestId',
    'month',
    'interestCount',
  ]);

  const isCustomize = month === customDurationValue;

  const currentOften = useMemo(() => {
    return vipOften.find((item) => `${item.mount}` === month);
  }, [month, vipOften]);

  console.log(interestId, currentOften, interestCount);

  const hasOften = orderType === 'upgrade' || !!currentOften || isCustomize;

  const priceQuery = useQuery({
    queryFn: () => {
      return orderPrice({
        interestCount: interestCount,
        interestId: Number(interestId),
        month: isCustomize ? undefined : currentOften?.mount,
        orderType,
        teamId: currentTeam!.id,
        days: isCustomize ? Number(form.getValues().days) : undefined,
      });
    },
    queryKey: [
      'orderPrice',
      {
        interestCount,
        interestId,
        month: currentOften?.mount,
        orderType,
        teamId: currentTeam?.id,
        days: form.getValues().days,
      },
    ],
    enabled:
      !!interestId &&
      !!interestCount &&
      !!currentTeam &&
      hasOften &&
      !!form.getValues().days,
  });

  useEffect(() => {
    if (priceQuery?.data && needPay) {
      form.setValue(
        'payAmount',
        (
          priceQuery.data.orderAmount - priceQuery.data.discountAmount
        )?.toString()
      );
    }
  }, [priceQuery?.data]);

  useEffect(() => {
    if (!open) {
      form.reset();
      setOrderType('create');
      setCurrentTeam(undefined);
    }
  }, [form, open]);

  const setTeam = (team?: TeamOrderStatus) => {
    setCurrentTeam(team);
    if (team) {
      setOrderType(team.hasVip ? 'renew' : 'create');
    }
  };

  // 是否可以选数量
  const canSelectInterestCount = ['create', 'upgrade'].includes(orderType);

  const isSubmitting = [
    mutation.isPending,
    reNewMutation.isPending,
    upgradeMutation.isPending,
    giftMutation.isPending,
  ].some(Boolean);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default">{'创建订单'}</Button>
      </DialogTrigger>
      <DialogContent className="!max-w-none w-[700px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>创建订单</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4 m-4">
              <FormField
                control={form.control}
                name="teamId"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-24 flex-shrink-0">团队</FormLabel>
                    <SelectTeam
                      value={field.value}
                      onChange={(value) => {
                        setTeam(value);
                        field.onChange(value ? value.id.toString() : '');
                      }}
                    />
                  </FormItem>
                )}
              />
              <div className="flex gap-1">
                <div className="font-medium w-24 flex-shrink-0">当前权益</div>
                {currentTeam && currentTeam.hasVip ?
                  <ul className="flex flex-col gap-2 p-4 bg-background rounded-lg border">
                    <Row
                      label="权益包数量"
                      value={`${currentTeam.interestCount}`}
                    />
                    {/* <Row
                      label="时长"
                      value={
                        currentTeam.month > 0 ?
                          `${formatMount(currentTeam.month)}${currentTeam.freeMonth ? `（送${formatMount(currentTeam.freeMonth)}）` : ''}`
                        : `${currentTeam.day ?? '-'}天`
                      }
                    /> */}
                    {/* <Row
                      label="支付时间"
                      value={formatDate(currentTeam.payTime)}
                    /> */}
                    <Row
                      label="到期时间"
                      value={formatYearMonthDay(currentTeam.expirationTime)}
                    />
                    {/* <Row
                      label="剩余时间"
                      value={`${currentTeam.remainingDay}天`}
                    />
                    <Row
                      label="原订单金额"
                      value={<OrderPrice price={currentTeam.price} />}
                    />
                    <Row
                      label="剩余金额"
                      value={<OrderPrice price={currentTeam.balance} />}
                    /> */}
                  </ul>
                : <span>-</span>}
              </div>
              {/* <FormField
                control={form.control}
                name="interestId"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-24 flex-shrink-0">权益包</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择权益包" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {vipQuery.data?.interests.map((item) => (
                          <SelectItem
                            key={item.id}
                            value={`${item.id}`}
                          >{`¥${item.price}/${item.memberCount}人/${item.messageCount}条消息/${item.platformAccountCount}个账号`}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              /> */}
              {orderType !== 'create' && (
                <div className="flex items-center gap-1 space-y-0">
                  <span className="w-24 flex-shrink-0 font-medium text-sm">
                    订单类型
                  </span>
                  <RadioGroup
                    defaultValue="renew"
                    value={orderType}
                    onValueChange={(value) => {
                      if (value === 'upgrade') {
                        form.setValue(
                          'interestCount',
                          currentTeam!.interestCount
                        );
                        form.setValue('month', '');
                      }
                      if (value === 'gift') {
                        form.setValue('month', vipOften[0].mount.toString());
                      }
                      setOrderType(value as OrderType);
                      form.setValue('days', '1');
                    }}
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="renew" id="r1" />
                      <Label htmlFor="r1">续费</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="upgrade" id="r2" />
                      <Label htmlFor="r2">升级</Label>
                    </div>
                    {/* <div className="flex items-center space-x-2">
                      <RadioGroupItem value="gift" id="r3" />
                      <Label htmlFor="r3">赠送</Label>
                    </div> */}
                  </RadioGroup>
                </div>
              )}
              {canSelectInterestCount && (
                <FormField
                  control={form.control}
                  name="interestCount"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-24 flex-shrink-0">
                        权益包数量
                      </FormLabel>
                      <FormControl>
                        <StepInput
                          num={field.value}
                          setNum={field.onChange}
                          min={currentTeam?.interestCount ?? 1}
                          max={9999}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
              {orderType === 'gift' ?
                <FormField
                  control={form.control}
                  name="giftDays"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-24 flex-shrink-0">
                        赠送天数
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="w-50"
                          min={1}
                          step={1}
                          type="number"
                          {...field}
                          onChange={(e) => {
                            const inputValue = Number(e.target.value);
                            if (/^[1-9]\d*$/.test(String(inputValue))) {
                              field.onChange(inputValue);
                            } else {
                              field.onChange(1); // 如果输入不合法，则默认为1
                            }
                          }}
                        />
                      </FormControl>
                      <span className="text-sm text-muted-foreground ml-4">
                        赠送后到期日期：
                        {formatYearMonthDay(
                          addDuration(
                            field.value,
                            'day',
                            currentTeam?.expirationTime ?? new Date()
                          )
                        )}
                      </span>
                    </FormItem>
                  )}
                />
              : <>
                  {orderType !== 'upgrade' && (
                    <FormField
                      control={form.control}
                      name="month"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-1 space-y-0">
                          <FormLabel className="w-24 flex-shrink-0">
                            时长
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="请选择时长" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {vipOften.map((item) => (
                                <SelectItem
                                  key={item.mount}
                                  value={`${item.mount}`}
                                >{`${item.mount}个月${item.present ? `+送${item.present}个月` : ''}`}</SelectItem>
                              ))}
                              <SelectItem value={customDurationValue}>
                                自定义
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}
                  {isCustomize && (
                    <FormField
                      control={form.control}
                      name="days"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-1 space-y-0">
                          <FormLabel className="w-24 flex-shrink-0">
                            天数
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-1">
                              <Input
                                className="w-64"
                                type="number"
                                {...field}
                                onBlur={() => {
                                  const input = field.value;
                                  if (
                                    !input ||
                                    Number(input) < 1 ||
                                    isNaN(Number(input))
                                  ) {
                                    field.onChange(1);
                                  }
                                }}
                              />
                              <span>天</span>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  )}

                  <div className="flex items-center gap-1 space-y-0">
                    <span className="w-24 flex-shrink-0 font-medium text-sm">
                      {/* 续费到期 */}
                      开通到期
                    </span>
                    {formatYearMonthDay(
                      addDuration(
                        isCustomize ?
                          Number(form.watch('days'))
                        : (currentOften?.mount ?? 0) +
                            (currentOften?.present ?? 0),
                        isCustomize ? 'day' : 'month',
                        isExpiredFun(currentTeam?.expirationTime) ?
                          new Date()
                        : currentTeam?.expirationTime
                      )
                    )}
                  </div>
                  {priceQuery.isLoading ?
                    <div className="h-16 w-full flex items-center justify-start">
                      <Loading className="w-5 h-5" />
                    </div>
                  : priceQuery.isSuccess && (
                      <>
                        <div className="flex items-center">
                          <p className="text-sm w-24 flex-shrink-0 font-medium">
                            订单金额
                          </p>
                          <p className="text-base font-bold flex items-center gap-2">
                            <span>
                              ¥{formatPrice(priceQuery.data.orderAmount)}
                            </span>
                            {orderType === 'upgrade' && (
                              <HelpTooltop
                                title={
                                  <span className="flex flex-col gap-1">
                                    <span>{priceQuery.data.tipsCn}</span>
                                    <span>{priceQuery.data.tips}</span>
                                  </span>
                                }
                              />
                            )}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <p className="text-sm w-24 flex-shrink-0 font-medium">
                            优惠金额
                          </p>
                          <p className="text-base font-bold">
                            ¥
                            {formatPrice(
                              priceQuery.data.couponAmount +
                                priceQuery.data.discountAmount
                            )}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <p className="text-sm w-24 flex-shrink-0 font-medium">
                            应付金额
                          </p>
                          <p className="text-base font-bold">
                            ¥
                            {formatPrice(
                              priceQuery.data.orderAmount -
                                priceQuery.data.discountAmount
                            )}
                          </p>
                        </div>
                      </>
                    )
                  }
                  <FormField
                    control={form.control}
                    name="isPay"
                    render={({ field }) => (
                      <FormItem className="flex items-center gap-1 space-y-0">
                        <FormLabel className="w-24 flex-shrink-0">
                          {/* 是否支付 */}
                          是否赠送
                        </FormLabel>
                        <FormControl>
                          {/* <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          /> */}

                          <RadioGroup
                            className="flex gap-10"
                            value={field.value}
                            onValueChange={(value: string) => {
                              field.onChange(value);
                              if (value === '0' && priceQuery?.data) {
                                const amount =
                                  (priceQuery?.data?.orderAmount || 0) -
                                  (priceQuery?.data?.discountAmount || 0);
                                if (amount > 0) {
                                  form.setValue('payAmount', amount.toString());
                                }
                              } else {
                                form.setValue('payAmount', '');
                              }
                            }}
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="1" id="yes" />
                              <Label htmlFor="yes">是</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="0" id="deny" />
                              <Label htmlFor="deny">否</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  {needPay && (
                    <FormField
                      control={form.control}
                      name="payAmount"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-1 space-y-0">
                          <FormLabel className="w-24 flex-shrink-0">
                            折扣价
                          </FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <span className="flex-shrink-0">元</span>
                        </FormItem>
                      )}
                    />
                  )}
                </>
              }

              <FormField
                control={form.control}
                name="remark"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-24 flex-shrink-0">
                      备注（选填）
                    </FormLabel>
                    <Textarea
                      rows={4}
                      placeholder="请输入"
                      className="resize-none"
                      {...field}
                    />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={isSubmitting}
                disabled={isSubmitting}
                type="submit"
              >
                提交
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
