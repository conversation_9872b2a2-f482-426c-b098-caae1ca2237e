import { makeRequest } from './index';
import { <PERSON><PERSON>uer<PERSON>, PageResult } from '@/types/index';
import { AddQuestionInput, QuestionDto } from '@/types/question';

/**
 * 获取常见问题列表
 */
export const questionList = (params: PageQuery) => {
  return makeRequest<PageResult<QuestionDto>>({
    url: '/question',
    method: 'GET',
    params,
  });
};

/**
 * 新增常见问题
 */
export const addQuestionConfig = async (input: AddQuestionInput) => {
  const addData: AddQuestionInput = {
    title: input.title,
    sort: input.sort,
    questionUrl: input.questionUrl,
  };
  return makeRequest<object>({
    url: `/question`,
    method: 'Post',
    data: addData,
  });
};

/**
 * 更新常见问题
 * Put /ad/{id}
 */
export const setQuestionConfig = async (input: AddQuestionInput) => {
  const setData: AddQuestionInput = {
    title: input.title,
    sort: input.sort,
    questionUrl: input.questionUrl,
  };
  return makeRequest<object>({
    url: `/question/${input.id}`,
    method: 'Patch',
    data: setData,
  });
};

/**
 * 常见问题详情
 * @param id
 * @returns
 */
export const getQuestionDetail = async (id: string) => {
  return makeRequest<object>({
    url: `/question/${id}`,
    method: 'Get',
  });
};

/**
 * 删除常见问题
 * Delete /ad/{id}
 */
export const deleteQuestion = (id: string) => {
  return makeRequest<object>({
    url: `/question/${id}`,
    method: 'Delete',
  });
};
