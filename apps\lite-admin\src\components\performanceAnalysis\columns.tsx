import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { TeamStatistic } from '@/types/analysis';
import { HelpTooltip } from '@mono/ui/common/HelpTooltip';
import { Link } from '@tanstack/react-router';
import { Button } from '@mono/ui/button';
import { getDayEndTime, getTimestampByTime } from '@mono/utils/day';
const columnHelper = createColumnHelper<TeamStatistic>();

export const getColumns = (
  customerId?: string
): ColumnDef<TeamStatistic, never>[] => {
  return [
    columnHelper.accessor('createTime', {
      cell: (info) => (
        <span className="text-muted-foreground">{info.getValue()}</span>
      ),
      header: () => <span>时间</span>,
    }),
    columnHelper.accessor('registerTeamCount', {
      cell: (info) => info.getValue(),
      header: () => <span>注册有效团队数</span>,
    }),
    columnHelper.accessor('expiredTeamCount', {
      cell: (info) => (
        <Link
          to="/team"
          search={{
            salesType: 4,
            customerId: customerId,
            expiredStartTime: getTimestampByTime(info.row.original.createTime),
            expiredEndTime: getDayEndTime(info.row.original.createTime),
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => <span>过期未续费团队数</span>,
    }),
    columnHelper.accessor('paidTeamCount', {
      cell: (info) => (
        <Link
          to="/order"
          search={{
            salesType: 1,
            customerId: customerId,
            orderStatus: 'paid',
            createStartTime: getTimestampByTime(info.row.original.createTime),
            createEndTime: getDayEndTime(info.row.original.createTime),
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => (
        <span className="flex items-center gap-1">
          新购团队数
          <HelpTooltip title="首次付费的团队" />
        </span>
      ),
    }),
    columnHelper.accessor('renewTeamCount', {
      cell: (info) => (
        <Link
          to="/order"
          search={{
            salesType: 2,
            customerId: customerId,
            orderStatus: 'paid',
            createStartTime: getTimestampByTime(info.row.original.createTime),
            createEndTime: getDayEndTime(info.row.original.createTime),
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => (
        <span className="flex items-center gap-1">
          复购团队数
          <HelpTooltip title="第二次开通、续费、升级的团队数" />
        </span>
      ),
    }),

    columnHelper.accessor('conversionRate', {
      header: () => (
        <span className="flex items-center gap-1">
          转化率
          <HelpTooltip title="转化率=首次付费的团队/注册有效团队" />
        </span>
      ),
      cell: (info) => <span>{info.getValue()}%</span>,
    }),
    columnHelper.accessor('renewRate', {
      cell: (info) => <span>{info.getValue()}%</span>,
      header: () => (
        <span className="flex items-center gap-1">
          续费率
          <HelpTooltip title="续费率=续费团队数/（过期未续费团队数+续费团队数）" />
        </span>
      ),
    }),
  ];
};
