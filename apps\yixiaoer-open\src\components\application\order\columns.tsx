import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Order } from '@/types/order';
import { Badge } from '@mono/ui/badge';
import { orderTypeMap } from '@/config/order';

export function getTableColumns(): Array<ColumnDef<Order>> {
  return [
    {
      header: '订单号',
      accessorKey: 'orderNumber',
      cell: ({ row }) => {
        const { orderNo } = row.original;
        return <div className="font-medium text-left">{orderNo}</div>;
      },
    },
    {
      header: '团队信息',
      accessorKey: 'team',
      cell: ({ row }) => {
        const { teamName, code } = row.original;
        return (
          <div className="flex flex-col text-muted-foreground">
            <span>团队名称：{teamName || '-'}</span>
            <span>团队ID：{code || '-'}</span>
          </div>
        );
      },
    },
    {
      size: 180,
      header: '创建时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '订单类型',
      accessorKey: 'orderType',
      cell: ({ row }) => {
        const { orderType } = row.original;
        return orderType ?
            <Badge variant="outline">{orderTypeMap[orderType]}</Badge>
          : '-';
      },
    },
    {
      size: 180,
      header: '支付时间',
      accessorKey: 'payTime',
      cell: ({ row }) => {
        const { payTime } = row.original;
        return payTime ?
            <span className="text-muted-foreground">{formatDate(payTime)}</span>
          : '-';
      },
    },
    {
      header: '蚁贝',
      accessorKey: 'payAmount',
      cell: ({ row }) => {
        const { payAmount } = row.original;
        return <span>{payAmount || '-'}</span>;
      },
    },
  ];
}
