'use client';

import * as React from 'react';
import { Calendar as CalendarIcon, CircleX } from 'lucide-react';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import { Calendar } from '@mono/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { zhCN } from 'date-fns/locale';
import { format } from 'date-fns';

export function DatePickerWithRange({
  className,
  onChange,
}: {
  className?: string;
  onChange: (date: DateRange) => void;
}) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });
  const [open, setOpen] = React.useState(false);

  const handleDateChange = (date: DateRange | undefined) => {
    setDate(date);
    if (date?.from && date?.to) {
      date.to = new Date(new Date(date.to).setHours(23, 59, 59, 0));
      onChange(date);
      setOpen(!open);
    }
  };

  const handleOpenChange = () => {
    if (open && date!.to) {
      setDate({
        from: undefined,
        to: undefined,
      });
      onChange({
        from: undefined,
        to: undefined,
      });
    }
    setOpen(!open);
  };

  const clearDate = (event: React.MouseEvent<SVGSVGElement>) => {
    setDate({
      from: undefined,
      to: undefined,
    });
    onChange({
      from: undefined,
      to: undefined,
    });
    event.stopPropagation();
  };

  return (
    <div className={cn(className)}>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <span className="relative group">
            <Button
              id="date"
              variant={'outline'}
              className={cn(
                'min-w-[200px] w-full  justify-start text-left font-normal h-9',
                !date && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ?
                date.to ?
                  <>
                    {format(date.from, 'y-MM-dd', { locale: zhCN })} ~{' '}
                    {format(date.to, 'y-MM-dd', { locale: zhCN })}
                  </>
                : format(date.from, 'y-MM-dd', { locale: zhCN })
              : <span>请选择日期</span>}
            </Button>
            <CircleX
              color={'#808080'}
              size={16}
              fill={'#fff'}
              className={
                'absolute right-2 top-[10px] -translate-y-1/2 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-200'
              }
              onClick={(e) => clearDate(e)}
            />
          </span>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            locale={zhCN}
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleDateChange}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
