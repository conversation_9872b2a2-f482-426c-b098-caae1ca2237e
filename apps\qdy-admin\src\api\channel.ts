import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { Channel, ChannelBase, ChannelParams } from '@/types/channel';

// 获取渠道列表
export const getChannels = (params: PageQuery & ChannelParams) => {
  return makeRequest<PageData<Channel>>({
    url: '/channels',
    method: 'GET',
    params,
  });
};

// 创建渠道
export const createChannel = (params: ChannelBase) => {
  return makeRequest<Channel>({
    url: '/channels',
    method: 'POST',
    data: params,
  });
};

// 更新渠道
export const updateChannel = ({
  channelId,
  params,
}: {
  channelId: number;
  params: ChannelBase;
}) => {
  return makeRequest<Channel>({
    url: `/channels/${channelId}`,
    method: 'PUT',
    data: params,
  });
};

// 删除渠道
export const deleteChannel = (channelId: number) => {
  return makeRequest({
    url: `/channels/${channelId}`,
    method: 'DELETE',
  });
};

// 修改渠道状态
//   PATCH /channels/{id}/status
//   接口ID：216004736
//   接口地址：https://app.apifox.com/link/project/4645177/apis/api-216004736
export const updateChannelStatus = ({
  channelId,
  status,
}: {
  channelId: number;
  status: boolean;
}) => {
  return makeRequest({
    url: `/channels/${channelId}/status`,
    method: 'PATCH',
    data: { status },
  });
};

// 修改渠道密码
// PATCH /channels/{id}/password
// 接口ID：233055334
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-233055334
export const updateChannelPassword = ({
  id,
  channelUserId,
  password,
}: {
  id: number;
  channelUserId: number;
  password: string;
}) => {
  return makeRequest({
    url: `/channels/${id}/password`,
    method: 'PATCH',
    data: {
      channelUserId: channelUserId,
      password: password,
    },
  });
};
