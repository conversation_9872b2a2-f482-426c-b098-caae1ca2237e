import { PaginationState } from '@tanstack/react-table';
import { DataTable } from '../dataTable';
import { TableSearch } from './TableSearch';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ChannelReq } from '@/types/channel';
import { getColumns } from './columns';
import { useMutation, useQuery } from '@tanstack/react-query';
import { channelList, deleteChannel, updateChannelStatus } from '@/api/channel';
import { ChannelForm } from './form';
import { alertBaseManager } from '../alertBase';

export const ChannelPage = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState<ChannelReq>();
  const editId = useRef<string>();

  const query = useQuery({
    queryKey: ['userList', pagination, filter],
    queryFn: () =>
      channelList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });

  const delMutation = useMutation({
    mutationFn: deleteChannel,
    onSuccess: () => {
      query.refetch();
    },
  });

  const enableMutation = useMutation({
    mutationFn: ({ id, value }: { id: string; value: boolean }) =>
      updateChannelStatus(id, value),
    onSuccess: () => {
      query.refetch();
    },
  });

  const columns = useMemo(() => {
    return getColumns(
      (item) => {
        alertBaseManager.open({
          title: '删除渠道',
          description: '确定删除该渠道吗？',
          onSubmit: () => {
            delMutation.mutate(item.id);
          },
        });
      },
      (item) => {
        editId.current = item.id;
        setOpen(true);
      },
      (item, status) => {
        enableMutation.mutate({ id: item.id, value: status });
      }
    );
  }, [delMutation, enableMutation]);
  useEffect(() => {
    if (!open) {
      console.log('open', open);
      editId.current = undefined;
    }
  }, [open]);
  return (
    <div className="flex flex-col gap-2 overflow-hidden">
      <div className="flex items-center gap-2">
        <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 });
            setFilter(value);
          }}
        />
        <ChannelForm
          open={open}
          onOpenChange={setOpen}
          channelId={editId.current}
          onSuccess={() => {
            query.refetch();
          }}
        />
      </div>

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
};
