import { makeRequest } from './index';
import { PageCountQ<PERSON>y, PageResult } from '@/types/index';
import {
  AddDesktopConfigInput,
  Desktop,
  DesktopUploadUrl,
  DesktopVersionListInput,
} from '@/types/desktopVersion';
import OSS from 'ali-oss';
import axios from 'axios';

/**
 * 获取版本列表
 * GET /desktop_app
 */
export const desktopVersionList = (
  params: PageCountQuery & DesktopVersionListInput
) => {
  if (params) {
    if (!params.publishStartTime) {
      delete params.publishStartTime;
    }
    if (!params.publishEndTime) {
      delete params.publishEndTime;
    }
    if (params?.type === 'all') {
      delete params.type;
    }
  }
  return makeRequest<PageResult<Desktop>>({
    url: '/online-scripts/desktop',
    method: 'GET',
    params,
  });
};

export const getUploadUrl = (fileName: string) => {
  return makeRequest<DesktopUploadUrl>({
    url: '/oss/desktop/upload-url',
    method: 'GET',
    params: {
      name: fileName,
    },
  });
};

/**
 * 新增版本信息
 * Post /online-scripts/desktop/{id}
 */
export const addDesktopConfig = async (input: AddDesktopConfigInput) => {
  let name = '';
  if (input.version && input.file) {
    name =
      import.meta.env.VITE_APP_NODE_ENV +
      '/' +
      input.version +
      '/app/app-release.apk';
    const urlResult = await getUploadUrl(name);
    await axios.put(urlResult.serviceUrl, input.file);
  }

  return makeRequest<object>({
    url: `/online-scripts/desktop`,
    method: 'Post',
    data: {
      type: input.type,
      version: input.version,
      isForce: input.isForce,
      notice: input.notice,
      scripts: name,
      requestUrl: input.requestUrl,
    },
    timeout: 300000,
  });
};

/**
 * 设置团队信息
 * Put /online-scripts/desktop/{id}
 */
export const setDesktopConfig = async (input: AddDesktopConfigInput) => {
  let name = '';
  if (input.version && input.file) {
    const credentials = await getStsCredentials();
    // 创建OSS客户端
    const client = new OSS({
      region: credentials.region,
      accessKeyId: credentials.accessKeyId,
      accessKeySecret: credentials.accessKeySecret,
      stsToken: credentials.securityToken,
      bucket: credentials.bucket,
      secure: true, // 使用 HTTPS
    });

    name =
      import.meta.env.VITE_APP_NODE_ENV +
      '/' +
      input.version +
      '/app/app-release.apk';
    // 上传文件
    await client.put(name, input.file, { timeout: 300000 });
  }

  return makeRequest<object>({
    url: `/online-scripts/desktop/${input.id}`,
    method: 'Put',
    data: {
      isForce: input.isForce,
      notice: input.notice,
      scripts: name ?? undefined,
    },
  });
};

/**
 * 删除版本信息
 * Delete /online-scripts/desktop/{id}
 */
export const deleteDesktop = (id: string) => {
  return makeRequest({
    url: `/online-scripts/desktop/${id}`,
    method: 'Delete',
  });
};

/**
 * 获取最新版本信息
 * GET /online-scripts/desktop/latest
 */
export const getDesktopLatest = (type: string) => {
  const params = {
    type: type,
  };
  return makeRequest<{
    version: string;
  }>({
    url: `/online-scripts/desktop/latest`,
    method: 'GET',
    params,
  });
};

export const getStsCredentials = () => {
  return makeRequest<{
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucket: string;
  }>({
    url: `/oss/sts`,
    method: 'GET',
  });
};
