import { PageCountQuery } from './index';

// 开放平台用户类型
export interface OpenPlatformUser {
  id: string;
  phone: string;
  nickname: string;
  applicationCount: number;
  status: number;
  remark: string;
  createdAt: string;
  updatedAt: string;
}

// 开放平台用户查询参数
export interface OpenPlatformUserParams extends PageCountQuery {
  phone?: string;
  registeredStartTime?: number;
  registeredEndTime?: number;
}

// 设置价格请求参数
export interface SetPriceParams {
  userId: string;
  accountPrice: number;
  trafficPrice: number;
}

export interface SetRemarkParams {
  userId: string;
  remark: string; // Added remark property
  // other properties
}

// 开放平台订单类型
export interface OpenPlatformOrder {
  id: string;
  orderNo: string;
  appId: string;
  orderType: 'purchase' | 'gift';
  amount: number;
  status: 'pending' | 'completed' | 'cancelled' | 'failed';
  createdAt: string;
  updatedAt: string;
  userId: string;
  phone: string;
  description?: string;
}

// 开放平台订单查询参数
export interface OpenPlatformOrderParams extends PageCountQuery {
  orderNo?: string;
  appId?: string;
  orderType?: 'BANK_TRANSFER' | 'GIFT';
  startTime?: string;
  endTime?: string;
}

// 订单详情
export interface OpenPlatformOrderDetail extends OpenPlatformOrder {
  userInfo: {
    phone: string;
    nickname?: string;
  };
  appInfo: {
    name: string;
    appId: string;
  };
  paymentInfo?: {
    method: string;
    transactionId?: string;
    paidAt?: string;
  };
}

// 对公转账开通状态
export interface CorporateTransferStatus {
  orderId: string;
  isEnabled: boolean;
  enabledAt?: string;
  disabledAt?: string;
}

// 对公转账开通请求参数
export interface CorporateTransferParams {
  rechargeOrderNo: string;
  enable: boolean;
  remark?: string;
}

// 订单类型映射
export const orderTypeMap = {
  create: '开通',
  renew: '续费',
  upgrade: '升级',
  gift: '赠送',
  open_platform_account_points: '账号',
  open_platform_traffic: '流量',
} as const;

// 充值类型映射
export const rechargeTypeMap = {
  BANK_TRANSFER: '购买',
  GIFT: '赠送',
} as const;

// 订单状态映射
export const orderStatusMap = {
  pending: '待支付',
  paid: '已完成',
  cancelled: '已取消',
  refunded: '退款',
} as const;

// 订单状态样式映射
export const orderStatusStyleMap = {
  pending: 'text-yellow-600 bg-yellow-50',
  paid: 'text-green-600 bg-green-50',
  cancelled: 'text-gray-600 bg-gray-50',
  refunded: 'text-red-600 bg-red-50',
} as const;

// ==================== 应用管理相关类型 ====================

// 开放平台应用类型
export interface OpenPlatformApp {
  id: string;
  appId: string;
  name: string;
  userCount: number;
  teamCount: number;
  totalAccountCount: number;
  totalTraffic: number;
  totalTrafficMB: number;
  usedTraffic: number;
  usedTrafficMB: number;
  accountPrice: number;
  trafficPrice: number;
  packagePrice: number;
  availableBalance: number; // 蚁币余额
  status: number;
  createdAt: number;
  updatedAt: number;
}

// 开放平台应用查询参数
export interface OpenPlatformAppParams {
  userId: string;
  appName?: string;
  appType?: string;
}

// 应用状态映射
export const appStatusMap = {
  0: '待审',
  1: '正常',
  2: '拒绝',
} as const;

// 应用状态样式映射
export const appStatusStyleMap = {
  0: 'text-yellow-600 bg-yellow-50',
  1: 'text-green-600 bg-green-50',
  2: 'text-red-600 bg-red-50',
} as const;

// 设置应用价格请求参数
export interface SetAppPriceParams {
  id: string;
  accountPrice: number;
  trafficPrice: number;
  packagePrice: number;
}

// ==================== 充值管理相关类型 ====================

// 充值订单类型
export interface RechargeOrder {
  id: string;
  applicationId: string;
  applicationName: string;
  openPlatformUserId: string;
  phone: string;
  rechargeOrderNo: string;
  rechargeType: 'BANK_TRANSFER' | 'GIFT';
  rechargeAmount: number;
  virtualCoinAmount: number;
  paymentAmount: number;
  rechargeStatus: 'PENDING | SUCCESS | FAILED | CANCELLED';
  rechargeTime: number;
  remark: string;
  operatorId: string;
  createdAt: number;
  updatedAt: number;
}

// 充值订单查询参数
export interface RechargeOrderParams extends PageCountQuery {
  phone?: string; // 手机号
  rechargeOrderNo?: string; // 充值订单号
  appName?: string; // 应用名称
  appId?: string; // 应用ID
  rechargeType?: 'BANK_TRANSFER' | 'GIFT' | ''; // 订单类型
  startTime?: string; // 完成时间开始
  endTime?: string; // 完成时间结束
}

// 创建充值订单参数
export interface CreateRechargeOrderParams {
  phone: string;
  applicationId: string;
  virtualCoinAmount: number;
  rechargeType: 'BANK_TRANSFER' | 'GIFT';
  paymentAmount: number;
  remark?: string;
}

// 充值订单状态映射
export const rechargeOrderStatusMap = {
  PENDING: '待支付',
  SUCCESS: '已完成',
  CANCELLED: '已取消',
  FAILED: '失败',
} as const;

// 充值订单状态样式映射
export const rechargeOrderStatusStyleMap = {
  PENDING: 'text-yellow-600 bg-yellow-50',
  SUCCESS: 'text-green-600 bg-green-50',
  CANCELLED: 'text-gray-600 bg-gray-50',
  FAILED: 'text-red-600 bg-red-50',
} as const;

// ==================== 扩展订单类型 ====================

// 扩展的开放平台订单类型
export interface ExtendedOpenPlatformOrder {
  id: string;
  orderNo: string;
  orderStatus: string;
  orderType: string;
  resourceType: string;
  teamId: string;
  teamName: string;
  code: string;
  appId: string;
  applicationName: string;
  userId: string;
  totalAmount: number;
  payableAmount: number;
  payAmount: number;
  accountCapacity: number;
  trafficCount: number;
  duration: number;
  startTime: number;
  endTime: number;
  trafficExpiredAt: number;
  payTime: number;
  remark: string;
  createdAt: number;
  updatedAt: number;
}

// 扩展的开放平台订单查询参数
export interface ExtendedOpenPlatformOrderParams extends PageCountQuery {
  orderNo?: string;
  appId?: string;
  code?: string;
  orderType?: 'open_platform_account_points' | 'open_platform_traffic';
  paymentStartTime?: string;
  paymentEndTime?: string;
}

// 购买类型映射
export const purchaseTypeMap = {
  account: '账号',
  traffic: '流量',
} as const;

// 购买类型样式映射
export const purchaseTypeStyleMap = {
  account: 'text-blue-600 bg-blue-50',
  traffic: 'text-purple-600 bg-purple-50',
} as const;
