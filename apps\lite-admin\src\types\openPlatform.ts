import { PageCountQuery } from './index';

// 开放平台用户类型
export interface OpenPlatformUser {
  id: string;
  phone: string;
  nickname: string;
  applicationCount: number;
  status: number;
  remark: string;
  createdAt: string;
  updatedAt: string;
}

// 开放平台用户查询参数
export interface OpenPlatformUserParams extends PageCountQuery {
  phone?: string;
  registeredStartTime?: number;
  registeredEndTime?: number;
}

// 设置价格请求参数
export interface SetPriceParams {
  userId: string;
  accountPrice: number;
  trafficPrice: number;
}

export interface SetRemarkParams {
  userId: string;
  remark: string; // Added remark property
  // other properties
}

// 开放平台订单类型
export interface OpenPlatformOrder {
  id: string;
  orderNo: string;
  appId: string;
  orderType: 'purchase' | 'gift';
  amount: number;
  status: 'pending' | 'completed' | 'cancelled' | 'failed';
  createdAt: string;
  updatedAt: string;
  userId: string;
  phone: string;
  description?: string;
}

// 开放平台订单查询参数
export interface OpenPlatformOrderParams extends PageCountQuery {
  orderNo?: string;
  appId?: string;
  orderType?: 'purchase' | 'gift';
  startTime?: string;
  endTime?: string;
}

// 订单详情
export interface OpenPlatformOrderDetail extends OpenPlatformOrder {
  userInfo: {
    phone: string;
    nickname?: string;
  };
  appInfo: {
    name: string;
    appId: string;
  };
  paymentInfo?: {
    method: string;
    transactionId?: string;
    paidAt?: string;
  };
}

// 对公转账开通状态
export interface CorporateTransferStatus {
  orderId: string;
  isEnabled: boolean;
  enabledAt?: string;
  disabledAt?: string;
}

// 对公转账开通请求参数
export interface CorporateTransferParams {
  orderId: string;
  enable: boolean;
  remark?: string;
}

// 订单类型映射
export const orderTypeMap = {
  purchase: '购买',
  gift: '赠送',
} as const;

// 订单状态映射
export const orderStatusMap = {
  pending: '待支付',
  completed: '已完成',
  cancelled: '已取消',
  failed: '失败',
} as const;

// 订单状态样式映射
export const orderStatusStyleMap = {
  pending: 'text-yellow-600 bg-yellow-50',
  completed: 'text-green-600 bg-green-50',
  cancelled: 'text-gray-600 bg-gray-50',
  failed: 'text-red-600 bg-red-50',
} as const;

// ==================== 应用管理相关类型 ====================

// 开放平台应用类型
export interface OpenPlatformApp {
  id: string;
  appId: string;
  name: string;
  userCount: number;
  teamCount: number;
  totalAccountCount: number;
  totalTraffic: number;
  totalTrafficMB: number;
  usedTraffic: number;
  usedTrafficMB: number;
  accountPrice: number;
  trafficPrice: number;
  payAmount: number;
  status: number;
  createdAt: number;
  updatedAt: number;
}

// 开放平台应用查询参数
export interface OpenPlatformAppParams {
  userId: string;
  appName?: string;
  appType?: string;
}

// 应用状态映射
export const appStatusMap = {
  0: '正常',
  1: '暂停',
  2: '禁用',
} as const;

// 应用状态样式映射
export const appStatusStyleMap = {
  0: 'text-green-600 bg-green-50',
  1: 'text-yellow-600 bg-yellow-50',
  2: 'text-red-600 bg-red-50',
} as const;

// 设置应用价格请求参数
export interface SetAppPriceParams {
  id: string;
  accountPrice: number;
  trafficPrice: number;
}

// ==================== 扩展订单类型 ====================

// 扩展的开放平台订单类型
export interface ExtendedOpenPlatformOrder extends OpenPlatformOrder {
  teamInfo: string;
  teamId: string;
  purchaseType: 'account' | 'traffic';
  paymentTime?: string;
  consumedAntCoins: number;
}

// 扩展的开放平台订单查询参数
export interface ExtendedOpenPlatformOrderParams extends PageCountQuery {
  orderNo?: string;
  appId?: string;
  teamId?: string;
  orderType?: 'purchase' | 'gift';
  purchaseType?: 'account' | 'traffic';
  startTime?: string;
  endTime?: string;
  paymentStartTime?: string;
  paymentEndTime?: string;
}

// 购买类型映射
export const purchaseTypeMap = {
  account: '账号',
  traffic: '流量',
} as const;

// 购买类型样式映射
export const purchaseTypeStyleMap = {
  account: 'text-blue-600 bg-blue-50',
  traffic: 'text-purple-600 bg-purple-50',
} as const;
