import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import {
  RechargeOrder,
  rechargeOrderStatusMap,
  rechargeOrderStatusStyleMap,
  rechargeTypeMap,
} from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye, CreditCard } from 'lucide-react';

interface RechargeColumnsProps {
  onViewDetail: (order: RechargeOrder) => void;
  onCorporateTransfer: (order: RechargeOrder) => void;
}

export function getRechargeColumns({
  onViewDetail,
  onCorporateTransfer,
}: RechargeColumnsProps): ColumnDef<RechargeOrder>[] {
  return [
    {
      accessorKey: 'rechargeOrderNo',
      header: '充值订单号',
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {row.getValue('rechargeOrderNo')}
        </div>
      ),
    },
    {
      accessorKey: 'phone',
      header: '手机号码',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('phone')}</div>
      ),
    },
    {
      accessorKey: 'rechargeType',
      header: '订单类型',
      cell: ({ row }) => {
        const type = row.getValue(
          'rechargeType'
        ) as keyof typeof rechargeTypeMap;
        return <Badge variant="outline">{rechargeTypeMap[type]}</Badge>;
      },
    },
    {
      accessorKey: 'rechargeAmount',
      header: '购买数量',
      cell: ({ row }) => {
        const quantity = row.getValue('rechargeAmount') as number;
        return (
          <div className="text-sm font-medium text-orange-600">
            {quantity.toLocaleString()} 蚁币
          </div>
        );
      },
    },
    {
      accessorKey: 'paymentAmount',
      header: '付款金额',
      cell: ({ row }) => {
        const amount = row.getValue('paymentAmount') as number;
        return <div className="text-sm font-medium">¥{amount.toFixed(2)}</div>;
      },
    },
    {
      accessorKey: 'totalTrafficMB',
      header: '应用信息',
      cell: ({ row }) => {
        const { applicationId, applicationName } = row.original;
        return (
          <div className="text-sm font-medium">
            {applicationName}
            <br />
            {applicationId}
          </div>
        );
      },
    },
    {
      accessorKey: 'rechargeStatus',
      header: '订单状态',
      cell: ({ row }) => {
        const status = row.getValue(
          'rechargeStatus'
        ) as keyof typeof rechargeOrderStatusMap;
        return (
          <Badge className={rechargeOrderStatusStyleMap[status]}>
            {rechargeOrderStatusMap[status]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'rechargeTime',
      header: '完成时间',
      cell: ({ row }) => {
        const timestamp = row.getValue('rechargeTime') as number;
        return (
          <div className="text-sm">
            {timestamp ?
              format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')
            : '-'}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const order = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetail(order)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              详情
            </Button>
            {(order.rechargeStatus as keyof typeof rechargeOrderStatusMap) ===
              'PENDING' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCorporateTransfer(order)}
                className="flex items-center gap-1"
              >
                <CreditCard className="w-4 h-4" />
                对公转账开通
              </Button>
            )}
          </div>
        );
      },
    },
  ];
}
