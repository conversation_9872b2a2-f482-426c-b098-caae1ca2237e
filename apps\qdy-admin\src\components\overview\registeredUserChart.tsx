import { useQuery } from '@tanstack/react-query';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { getRegisterDau } from '@/api/overview';

const chartConfig = {
  registerCount: {
    label: '注册用户数',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function RegisteredUserChart() {
  const { data } = useQuery({
    queryKey: ['getRegisterDau'],
    queryFn: getRegisterDau,
  });
  return (
    <Card className="h-full overflow-hidden flex flex-col flex-1">
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0">
        <div className="flex flex-shrink-0 flex-col justify-center gap-1 px-6 py-4">
          <CardTitle className="flex">30日注册用户数</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-2 flex-1 h-0">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <AreaChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
              top: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              type="category"
              dataKey="createTime"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Area
              dataKey="registerCount"
              type="natural"
              fill="var(--color-registerCount)"
              fillOpacity={0.4}
              stroke="var(--color-registerCount)"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
