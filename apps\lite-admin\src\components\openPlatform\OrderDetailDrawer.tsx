import { useQuery } from '@tanstack/react-query';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@mono/ui/sheet';
import { Badge } from '@mono/ui/badge';
import { Separator } from '@mono/ui/separator';
import { getOpenPlatformOrderDetail } from '@/api/openPlatform';
import { orderTypeMap, orderStatusMap, orderStatusStyleMap } from '@/types/openPlatform';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';

interface OrderDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  orderId: string | null;
}

export function OrderDetailDrawer({ open, onClose, orderId }: OrderDetailDrawerProps) {
  const { data: orderDetail, isLoading, error } = useQuery({
    queryKey: ['openPlatformOrderDetail', orderId],
    queryFn: () => getOpenPlatformOrderDetail(orderId!),
    enabled: !!orderId && open,
  });

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>订单详情</SheetTitle>
        </SheetHeader>

        <div className="mt-6">
          {isLoading && (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          )}

          {error && (
            <div className="text-center text-red-500">
              加载失败: {error instanceof Error ? error.message : '未知错误'}
            </div>
          )}

          {orderDetail && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">基本信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单号:</span>
                    <span className="font-mono">{orderDetail.orderNo}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单ID:</span>
                    <span className="font-mono">{orderDetail.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单类型:</span>
                    <span>{orderTypeMap[orderDetail.orderType]}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单状态:</span>
                    <Badge className={orderStatusStyleMap[orderDetail.status]}>
                      {orderStatusMap[orderDetail.status]}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单金额:</span>
                    <span className="font-medium">¥{orderDetail.amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">下单时间:</span>
                    <span>{format(new Date(orderDetail.createdAt), 'yyyy-MM-dd HH:mm:ss')}</span>
                  </div>
                  {orderDetail.description && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">订单描述:</span>
                      <span>{orderDetail.description}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* 用户信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">用户信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">用户ID:</span>
                    <span className="font-mono">{orderDetail.userId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">手机号:</span>
                    <span>{orderDetail.userInfo.phone}</span>
                  </div>
                  {orderDetail.userInfo.nickname && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">昵称:</span>
                      <span>{orderDetail.userInfo.nickname}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* 应用信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">应用信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">AppID:</span>
                    <span className="font-mono">{orderDetail.appInfo.appId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">应用名称:</span>
                    <span>{orderDetail.appInfo.name}</span>
                  </div>
                </div>
              </div>

              {/* 支付信息 */}
              {orderDetail.paymentInfo && (
                <>
                  <Separator />
                  <div>
                    <h3 className="text-lg font-medium mb-4">支付信息</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">支付方式:</span>
                        <span>{orderDetail.paymentInfo.method}</span>
                      </div>
                      {orderDetail.paymentInfo.transactionId && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">交易号:</span>
                          <span className="font-mono">{orderDetail.paymentInfo.transactionId}</span>
                        </div>
                      )}
                      {orderDetail.paymentInfo.paidAt && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">支付时间:</span>
                          <span>{format(new Date(orderDetail.paymentInfo.paidAt), 'yyyy-MM-dd HH:mm:ss')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
