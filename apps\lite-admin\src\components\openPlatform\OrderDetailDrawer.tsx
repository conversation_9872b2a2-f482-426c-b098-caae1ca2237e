import { Sheet, She<PERSON><PERSON>ontent, SheetHeader, SheetTitle } from '@mono/ui/sheet';
import { Badge } from '@mono/ui/badge';
import { Separator } from '@mono/ui/separator';
import {
  orderTypeMap,
  orderStatusMap,
  orderStatusStyleMap,
  ExtendedOpenPlatformOrder,
} from '@/types/openPlatform';
import { format } from 'date-fns';

interface OrderDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  orderDetail: ExtendedOpenPlatformOrder | null;
}

export function OrderDetailDrawer({
  open,
  onClose,
  orderDetail,
}: OrderDetailDrawerProps) {
  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>订单详情</SheetTitle>
        </SheetHeader>

        <div className="mt-6">
          {orderDetail && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">基本信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单号:</span>
                    <span className="font-mono">{orderDetail.orderNo}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单ID:</span>
                    <span className="font-mono">{orderDetail.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单类型:</span>
                    <span>
                      {
                        orderTypeMap[
                          orderDetail.orderType as keyof typeof orderTypeMap
                        ]
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单状态:</span>
                    <Badge
                      className={
                        orderStatusStyleMap[
                          orderDetail.orderStatus as keyof typeof orderStatusMap
                        ]
                      }
                    >
                      {
                        orderStatusMap[
                          orderDetail.orderStatus as keyof typeof orderStatusMap
                        ]
                      }
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单金额:</span>
                    <span className="font-medium">
                      ¥{orderDetail.payAmount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">下单时间:</span>
                    <span>
                      {format(
                        new Date(orderDetail.createdAt),
                        'yyyy-MM-dd HH:mm:ss'
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付时间:</span>
                    <span>
                      {orderDetail.payTime ?
                        format(orderDetail.payTime, 'yyyy-MM-dd HH:mm:ss')
                      : '-'}
                    </span>
                  </div>
                  {orderDetail.remark && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">订单描述:</span>
                      <span>{orderDetail.remark}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* 用户信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">团队信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">团队ID:</span>
                    <span className="font-mono">{orderDetail.teamId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">团队Code:</span>
                    <span className="font-mono">{orderDetail.code}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">团队名称:</span>
                    <span>{orderDetail.teamName}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* 应用信息 */}
              <div>
                <h3 className="text-lg font-medium mb-4">应用信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">AppID:</span>
                    <span className="font-mono">{orderDetail.appId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">应用名称:</span>
                    <span>{orderDetail.applicationName}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
