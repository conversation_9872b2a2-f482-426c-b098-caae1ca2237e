import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { Member } from '@/types/user';
import { AvatarCard } from '@mono/ui/common/AvatarCard';
import { Button } from '@mono/ui/button';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { Link } from '@tanstack/react-router';
import { Badge } from '@mono/ui/badge';
// import { Button } from "@mono/ui/button";

export function getTableColumns(
  // 更换客服
  onSwitchCustomer: (id: string) => void,
  // 跟进记录
  onFollowRecord: (id: string) => void,
  onHandleAdd: (followUserId: string) => void,
  onLockDevice: (id: string) => void
): Array<ColumnDef<Member>> {
  return [
    {
      header: '用户',
      accessorKey: 'id',
      cell: ({ row }) => {
        const { nickName, phone, avatar, account } = row.original;
        return (
          <div className="w-44">
            <AvatarCard title={nickName} src={avatar} subtitle={phone || account} />
          </div>
        );
      },
    },
    {
      header: '时间',
      accessorKey: 'createAt',
      cell: ({ row }) => {
        const { createdAt, updatedAt } = row.original;
        return (
          <div className="flex flex-col gap-1 w-56">
            <span className="text-muted-foreground">
              注册：{formatDate(createdAt)}
            </span>
            <span className="text-muted-foreground">
              活跃：{formatDate(updatedAt)}
            </span>
          </div>
        );
      },
    },
    {
      header: '跟进记录',
      accessorKey: 'followRecordCount',
      cell: ({ row }) => {
        const { followRecordCount, newfollowRecord, id } = row.original;
        return followRecordCount ?
            <div className="flex flex-col items-start">
              <span className="text-muted-foreground line-clamp-2">
                {newfollowRecord}
              </span>
              <span
                onClick={() => onFollowRecord(id)}
                className="cursor-pointer"
              >
                共{followRecordCount}条
              </span>
            </div>
          : '-';
      },
    },
    {
      header: '团队数',
      accessorKey: 'teamCount',
      cell: ({ row }) => {
        const { teamCount, phone } = row.original;
        return (
          <Link to="/team" search={{ phone }}>
            <Button variant="link">{teamCount}</Button>
          </Link>
        );
      },
    },
    {
      header: '渠道码',
      accessorKey: 'channelCode',
      cell: ({ row }) => {
        const { channelCode } = row.original;
        return channelCode ?
            <Button variant="link">{channelCode}</Button>
          : '-';
      },
    },
    {
      header: '客服归属',
      accessorKey: 'customerName',
      cell: ({ row }) => {
        const { customerName } = row.original;
        return <span className="text-muted-foreground">{customerName}</span>;
      },
    },
    {
      header: '注册来源',
      accessorKey: 'registrationSource',
      cell: ({ row }) => {
        const { registrationSource } = row.original;
        return registrationSource ?
            <Badge variant="outline">
              {registrationSource === 'open_platform_app' ?
                '开放平台'
              : registrationSource}
            </Badge>
          : '-';
      },
    },
    {
      header: '应用名称',
      accessorKey: 'applicationName',
      cell: ({ row }) => {
        const { applicationName } = row.original;
        return applicationName ?
            <span className="text-muted-foreground">{applicationName}</span>
          : '-';
      },
    },
    {
      header: '操作',
      accessorKey: 'operation',
      cell: ({ row }) => {
        const { id, customerName } = row.original;
        return (
          <DropdownMenuComponent>
            <DropdownMenuItem onClick={() => onSwitchCustomer(id)}>
              {customerName ? '更换客服' : '分配客服'}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onHandleAdd(id)}>
              添加记录
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onLockDevice(id)}>
              查看设备
            </DropdownMenuItem>
          </DropdownMenuComponent>
        );
      },
    },
  ];
}
