import {
  PerformanceSearchParams,
  PerformanceStatistic,
  SummaryData,
} from '@/types/performance';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';

// 团队转化率/续费率
// GET /overview/team/rate
// 接口ID：272735460
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-272735460
export const getTeamRate = (params: PageQuery & PerformanceSearchParams) => {
  return makeRequest<PageData<PerformanceStatistic> & SummaryData>({
    url: '/overview/team/rate',
    method: 'GET',
    params: params,
  });
};
