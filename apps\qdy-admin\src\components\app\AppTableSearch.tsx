import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { AppPlatformType, AppVersionParams } from '@/types/app';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';

const formSchema = z.object({
  releaseTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  type: z
    .enum([AppPlatformType.Ios, AppPlatformType.Android, AppPlatformType.All])
    .optional(),
});

export function AppTableSearch({
  values,
  onSearch,
  onCreate,
}: {
  values: AppVersionParams;
  onSearch: (values: AppVersionParams) => void;
  onCreate: (value: boolean) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: values,
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: AppVersionParams = {
      releaseTimeMin: values.releaseTimeRange?.from?.getTime(),
      releaseTimeMax: values.releaseTimeRange?.to?.getTime(),
      type: values.type?.trim() as AppPlatformType,
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <div className="flex justify-between">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-wrap gap-4 justify-start p-0.5"
        >
          <FormField
            control={form.control}
            name="releaseTimeRange"
            render={({ field }) => (
              <FormItem className="flex items-center gap-1 space-y-0">
                <FormLabel className="">发布时间</FormLabel>
                <CalendarRange
                  value={field.value}
                  onChange={field.onChange}
                ></CalendarRange>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem className="flex items-center gap-1 space-y-0">
                <FormLabel>设备类型</FormLabel>
                <FormControl>
                  <Select
                    value={field.value || AppPlatformType.All}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select a fruit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {[
                          {
                            value: AppPlatformType.All,
                            label: '全部',
                          },
                          {
                            value: AppPlatformType.Ios,
                            label: 'Ios',
                          },
                          {
                            value: AppPlatformType.Android,
                            label: 'Android',
                          },
                        ].map((item) => (
                          <SelectItem key={item.value} value={item.value}>
                            {item.label}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </FormControl>
              </FormItem>
            )}
          />
          <Button type="submit">搜索</Button>
        </form>
      </Form>
      <Button onClick={() => onCreate(true)}>发布版本</Button>
    </div>
  );
}
