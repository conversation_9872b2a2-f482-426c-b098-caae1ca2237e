// import { ChangeEvent } from 'react';
import { FormField } from '@mono/ui/form';
// import { Control, Controller, FieldValues, Path } from 'react-hook-form';

// interface Transform<TFieldValue, TInputValue> {
//   input: (value: TFieldValue) => TInputValue;
//   output: (e: ChangeEvent<HTMLInputElement>) => TFieldValue;
// }

// interface ControllerPlusProps<
//   TFormValues extends FieldValues,
//   TFieldValue,
//   TInputValue,
// > {
//   control: Control<TFormValues>;
//   transform: Transform<TFieldValue, TInputValue>;
//   name: Path<TFormValues>;
//   defaultValue?: TFieldValue;
// }

export const FormControllerPlus = ({
  control,
  name,
  transform,
  defaultValue,
}: React.ComponentProps<typeof FormField> & {
  transform: {
    input: (value: string) => string;
    output: (event: React.ChangeEvent<HTMLInputElement>) => string;
  };
}) => (
  <FormField
    defaultValue={defaultValue}
    control={control}
    name={name}
    render={({ field }) => (
      <input
        onChange={(e) => field.onChange(transform.output(e))}
        value={transform.input(field.value)}
      />
    )}
  />
);
