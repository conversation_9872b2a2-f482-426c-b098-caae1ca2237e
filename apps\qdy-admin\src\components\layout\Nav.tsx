import { Link } from '@tanstack/react-router';
// import { Spinner } from '../Spinner';
import { navs } from './navs';
import { Profile } from './Profile';
import logo from '@/assets/logo.svg';
import { useUserStore } from '@/store/user';
import { UserRound } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { userInfo } from '@/api/user';

// function RouterSpinner() {
//   const isLoading = useRouterState({ select: (s) => s.status === 'pending' });
//   return <Spinner show={isLoading} />;
// }

export function Nav() {
  const setUser = useUserStore((state) => state.setUser);

  const query = useQuery({
    queryKey: ['userInfo'],
    queryFn: userInfo,
  });

  if (query.data) {
    const index = navs.findIndex((x) => x.path === '/admin');
    if (index === -1) {
      if (query.data.role === 0) {
        navs.push({
          name: '管理员',
          icon: <UserRound className="h-5 w-5" />,
          path: '/admin',
        });
      }
    } else {
      if (query.data.role !== 0) {
        navs.splice(index, 1);
      }
    }
    setUser(query.data);
  }

  return (
    <aside className="flex-col border-r h-full overflow-hidden  bg-muted/40 flex">
      <div className="flex flex-1 h-0 flex-col gap-2">
        <div className="flex flex-shrink-0 h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link to="/" className="flex items-center gap-2 font-semibold">
            <img src={logo} className="h-6 w-6" />
            <span className="truncate max-w-36">后台管理系统</span>
          </Link>
          {/* <RouterSpinner /> */}
        </div>
        <div className="flex-1 h-0 overflow-auto">
          <nav className="grid items-start text-sm font-medium px-4 space-y-1">
            {navs.map((item) => (
              // <SauryTooltip side="right" tooltip={item.name}>
              <Link
                key={item.path}
                activeProps={{
                  className: 'bg-muted text-primary',
                }}
                className="flex items-center gap-3 rounded-lg px-3 py-3 text-muted-foreground transition-all hover:text-primary"
                to={item.path}
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
              // </SauryTooltip>
            ))}
          </nav>
        </div>
        <div className="p-4 py-6 flex-shrink-0">
          <Profile />
        </div>
      </div>
    </aside>
  );
}
