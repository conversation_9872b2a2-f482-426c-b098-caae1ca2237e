import { useMemo, useRef, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
import { DataTable } from '@/components/dataTable';
import { PaginationState } from '@tanstack/react-table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import { getTableColumns } from '@/components/question/columns';
import { deleteQuestion, questionList } from '@/api/question';
import { QuestionDto } from '@/types/question';
import { QuestionSetting } from '@/components/question/setting';
import { Button } from '@mono/ui/button';

export const Route = createFileRoute('/_home/question')({
  component: Question,
});

function Question() {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [dialog, setDialog] = useState(false);
  const mQuestion = useRef({} as QuestionDto);
  const nameRef = useRef(name);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(() => {
    return getTableColumns((question: QuestionDto, method: string) => {
      mQuestion.current = question;
      if (method == 'edit') {
        setOpen(true);
      }
      if (method == 'delete') {
        setDialog(true);
      }
    });
  }, []);

  const query = useQuery({
    queryKey: ['questionList', nameRef.current, pagination],
    queryFn: () =>
      questionList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  const handelDelete = async (id: string) => {
    await deleteQuestion(id);
    await queryClient.refetchQueries({
      queryKey: ['questionList'],
    });
  };

  const handleClick = () => {
    mQuestion.current = {
      id: '',
      title: '',
      sort: 1,
      questionUrl: '',
    };
    setOpen(true); // 打开弹框
  };
  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      <div className="flex pt-1 pb-4 px-1 gap-4">
        <Button onClick={() => handleClick()}>添加常见问题</Button>
      </div>

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <QuestionSetting
        question={mQuestion.current}
        open={open}
        setOpen={setOpen}
      />

      <AlertDialog open={dialog} onOpenChange={setDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定删除吗?</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handelDelete(mQuestion.current.id)}
            >
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
