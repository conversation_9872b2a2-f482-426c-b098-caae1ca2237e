import { ColumnDef } from '@tanstack/react-table';
import { FinancialRecord } from '@/types/team';

export function getFinancialRecordColumns(): Array<ColumnDef<FinancialRecord>> {
  return [
    {
      header: '金额',
      accessorKey: 'amount',
      cell: ({ row }) => {
        return (
          <span className="text-muted-foreground">{row.original.amount}</span>
        );
      },
    },
    {
      header: '类型',
      accessorKey: 'type',
      cell: ({ row }) => {
        return (
          <span className="text-muted-foreground">{row.original.type}</span>
        );
      },
    },
    {
      header: '备注',
      accessorKey: 'remark',
      cell: ({ row }) => {
        return (
          <span className="text-muted-foreground">{row.original.remark}</span>
        );
      },
    },
  ];
}
