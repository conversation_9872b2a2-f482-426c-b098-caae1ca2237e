import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { HelpTooltip } from '@mono/ui/common/HelpTooltip';
import { Link } from '@tanstack/react-router';
import { Button } from '@mono/ui/button';
import { formatBy, getDayStartTime, getDayEndTime } from '@mono/utils/day';
import { PerformanceStatistic } from '@/types/performance';
const columnHelper = createColumnHelper<PerformanceStatistic>();

export const getColumns = (): ColumnDef<PerformanceStatistic, never>[] => {
  return [
    columnHelper.accessor('statisticDate', {
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatBy(info.getValue(), 'YYYY-MM-DD')}
        </span>
      ),
      header: () => <span>时间</span>,
    }),
    columnHelper.accessor('registerTeamCount', {
      cell: (info) => info.getValue(),
      header: () => <span>注册有效团队数</span>,
    }),
    columnHelper.accessor('expiredTeamCount', {
      cell: (info) => (
        <Link
          to="/team"
          search={{
            salesType: 'Buy',
            expiredStartTime: getDayStartTime(info.row.original.statisticDate),
            expiredEndTime: getDayEndTime(info.row.original.statisticDate),
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => <span>过期未续费团队数</span>,
    }),
    columnHelper.accessor('paidTeamCount', {
      cell: (info) => (
        <Link
          to="/order"
          search={{
            salesType: 'FirstBuy',
            orderStartTime: getDayStartTime(info.row.original.statisticDate),
            orderEndTime: getDayEndTime(info.row.original.statisticDate),
            orderStatus: 'success',
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => (
        <span className="flex items-center gap-1">
          新购团队数
          <HelpTooltip title="首次开通VIP的团队数" />
        </span>
      ),
    }),
    columnHelper.accessor('renewTeamCount', {
      cell: (info) => (
        <Link
          to="/order"
          search={{
            salesType: 'ReBuy',
            orderStartTime: getDayStartTime(info.row.original.statisticDate),
            orderEndTime: getDayEndTime(info.row.original.statisticDate),
            orderStatus: 'success',
          }}
        >
          <Button variant="link">{info.getValue()}</Button>
        </Link>
      ),
      header: () => (
        <span className="flex items-center gap-1">
          复购团队数
          <HelpTooltip title="第二次开通，付费、升级的团队数" />
        </span>
      ),
    }),
    columnHelper.accessor('conversionRate', {
      header: () => (
        <span className="flex items-center gap-1">
          转化率
          <HelpTooltip title="转化率=新购团队数/注册有效团队数" />
        </span>
      ),
      cell: (info) => <span>{info.getValue()}%</span>,
    }),
    columnHelper.accessor('renewRate', {
      cell: (info) => <span>{info.getValue()}%</span>,
      header: () => (
        <span className="flex items-center gap-1">
          续费率
          <HelpTooltip title="续费率= 复购团队数/（过期未续费团队数+复购团队数）" />
        </span>
      ),
    }),
    columnHelper.accessor('payAmountTotal', {
      cell: (info) => <span>{info.getValue()}</span>,
      header: () => <span>金额</span>,
    }),
    columnHelper.accessor('customerUnitPrice', {
      cell: (info) => <span>{info.getValue()}</span>,
      header: () => <span>客单价</span>,
    }),
  ];
};
