import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import { Table } from '@mono/ui/common/TableScrollArea';
// import { ScrollArea } from '@mono/ui/scroll-area';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  PaginationState,
  OnChangeFn,
} from '@tanstack/react-table';
import { DataTablePagination } from '@/components/dataTablePagination';
import { LoadingContainer } from './loading';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data?: TData[];
  rowCount?: number;
  pagination: PaginationState;
  setPagination: OnChangeFn<PaginationState>;
}

export function DataTable<TData, TValue>({
  // setAccount,
  columns,
  data,
  rowCount,
  pagination,
  setPagination,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data: data ?? [],
    columns,
    rowCount,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    // debugTable: true,
    // defaultColumn: {
    //   size: 200,
    //   minSize: 200,
    //   maxSize: 200,
    // },
    // autoResetPageIndex: true,
  });
  return (
    <div className="flex flex-col flex-1 overflow-hidden justify-between gap-2 border rounded-md">
      {data ?
        <Table>
          <TableHeader className="sticky top-0 bg-muted z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="text-muted-foreground"
                    >
                      {header.isPlaceholder ? null : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ?
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  style={{
                    height: `50px`,
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      align="left"
                      key={cell.id}
                      style={{
                        width: cell.column.getSize(),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            : <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            }
          </TableBody>
        </Table>
      : <LoadingContainer />}
      <div className="flex-shrink-0 pb-2">
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}
