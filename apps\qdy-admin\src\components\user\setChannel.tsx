import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { LoadingButton } from '../loadingButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { setChannel } from '@/api/user';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { User } from '@/types/user';

const formSchema = z.object({
  channelCode: z.string().min(1, '请输入渠道码'),
});

export function SetChannel({
  open,
  setOpen,
  user,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User | undefined;
}) {
  const queryClient = useQueryClient();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      channelCode: '',
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: (values: z.infer<typeof formSchema>) =>
      setChannel(user ? user.id : -1, values.channelCode),
    onSuccess: () => {
      completeSubmit();
    },
  });

  function completeSubmit() {
    setOpen(false);
    form.reset();
    queryClient.refetchQueries({
      queryKey: ['getUsers'],
    });
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    mutation.mutate(values);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="!max-w-none w-[500px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle className="text-center">设置渠道</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4 m-4">
              <FormField
                control={form.control}
                name="channelCode"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-12 flex-shrink-0">
                        渠道码
                      </FormLabel>
                      <FormControl>
                        <Input {...field} autoComplete="off" />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-12" />
                  </div>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                确认设置
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
