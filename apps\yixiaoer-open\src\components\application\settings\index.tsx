import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ScrollArea } from '@mono/ui/scroll-area';
import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@mono/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@mono/ui/card';
import { Loader2, Save, Trash2 } from 'lucide-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
import { toast } from 'sonner';
import {
  getWebhookConfig,
  saveWebhookConfig,
  deleteWebhookConfig,
} from '@/api/webhook';

// 表单验证 schema
const webhookSchema = z.object({
  webhookUrl: z
    .string()
    .url('请输入有效的 URL 地址')
    .min(1, 'Webhook URL 不能为空'),
});

type WebhookFormData = z.infer<typeof webhookSchema>;

export function ApplicationSettings() {
  const params = useParams({ strict: false });
  const applicationId = params.applicationId as string;
  const queryClient = useQueryClient();

  const form = useForm<WebhookFormData>({
    resolver: zodResolver(webhookSchema),
    defaultValues: {
      webhookUrl: '',
    },
  });

  // 获取 Webhook 配置
  const { data: webhookConfig, isLoading } = useQuery({
    queryKey: ['webhookConfig', applicationId],
    queryFn: () => getWebhookConfig(applicationId),
    enabled: !!applicationId,
    retry: false,
  });

  // 设置表单默认值
  useEffect(() => {
    if (webhookConfig) {
      form.reset({
        webhookUrl: webhookConfig.webhookUrl || '',
      });
    }
  }, [webhookConfig, form]);

  // 保存配置
  const saveMutation = useMutation({
    mutationFn: (data: WebhookFormData) => {
      return saveWebhookConfig(applicationId, data);
    },
    onSuccess: () => {
      toast.success('Webhook 配置保存成功');
      queryClient.invalidateQueries({
        queryKey: ['webhookConfig', applicationId],
      });
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : '保存失败';
      toast.error(errorMessage);
    },
  });

  // 删除配置
  const deleteMutation = useMutation({
    mutationFn: () => deleteWebhookConfig(applicationId),
    onSuccess: () => {
      toast.success('Webhook 配置删除成功');
      form.reset({ webhookUrl: '' });
      queryClient.invalidateQueries({
        queryKey: ['webhookConfig', applicationId],
      });
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : '删除失败';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data: WebhookFormData) => {
    saveMutation.mutate(data);
  };

  const handleDelete = () => {
    if (confirm('确定要删除 Webhook 配置吗？此操作不可恢复。')) {
      deleteMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <ScrollArea className="w-full h-full">
      <div className="h-full flex flex-col items-center gap-6 p-5">
        <div className="w-full max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-semibold">
                Webhook 配置
              </CardTitle>
              <CardDescription>
                配置 Webhook URL
                以接收应用事件通知。当用户操作、订单变更等事件发生时，系统将向您的服务器发送
                HTTP POST 请求。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="webhookUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Webhook URL *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://your-domain.com/webhook"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          请输入您的服务器接收 Webhook 事件的 URL 地址
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-3">
                    <Button
                      type="submit"
                      disabled={saveMutation.isPending}
                      className="flex items-center gap-2"
                    >
                      {saveMutation.isPending ?
                        <Loader2 className="w-4 h-4 animate-spin" />
                      : <Save className="w-4 h-4" />}
                      保存配置
                    </Button>

                    {webhookConfig?.id && (
                      <Button
                        type="button"
                        variant="destructive"
                        onClick={handleDelete}
                        disabled={deleteMutation.isPending}
                        className="flex items-center gap-2"
                      >
                        {deleteMutation.isPending ?
                          <Loader2 className="w-4 h-4 animate-spin" />
                        : <Trash2 className="w-4 h-4" />}
                        删除配置
                      </Button>
                    )}
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  );
}
