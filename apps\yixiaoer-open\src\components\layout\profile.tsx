import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { userLogout } from '@/api/user';
import { useMutation } from '@tanstack/react-query';
import { logout } from '@/lib/utils/auth';
import { useUserStore } from '@/store/user';
import { useNavigate } from '@tanstack/react-router';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

export function Profile() {
  const user = useUserStore((state) => state.user);
  const navigate = useNavigate();

  const logoutMutation = useMutation({
    mutationFn: userLogout,
    onSuccess: () => {
      toast.success('退出登录成功');
      logout();
    },
    onError: (error) => {
      // 即使 API 调用失败，也要清除本地数据并跳转
      console.error('退出登录 API 调用失败:', error);
      toast.error('退出登录失败，但已清除本地数据');
      logout();
    },
  });

  const handleLogout = () => {
    // 显示确认提示
    if (window.confirm('确定要退出登录吗？')) {
      logoutMutation.mutate();
    }
  };

  const handleRechargeManagement = () => {
    navigate({ to: '/recharge-management' });
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="w-[30px] h-[30px] cursor-pointer">
          <AvatarImage src={''} alt="" />
          <AvatarFallback className="text-xs font-normal">
            {user?.userInfo?.nickname.toUpperCase().substring(0, 2)}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" side="bottom">
        <DropdownMenuItem onClick={handleRechargeManagement}>
          充值管理
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
          className="focus:bg-red-50 focus:text-red-600"
        >
          <div className="flex items-center gap-2">
            {logoutMutation.isPending && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
            <span>退出登录</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
