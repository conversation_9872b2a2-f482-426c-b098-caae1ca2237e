import { UserLogin } from '@/types/user';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

type UserState = {
  user?: UserLogin;
  setUser: (userLogin?: UserLogin) => void;
  clearUser: () => void;
};

export const useUserStore = create<UserState>()(
  persist(
    immer((set) => ({
      user: undefined,
      setUser: (user) => set({ user }),
      clearUser: () => set({ user: undefined }),
    })),
    {
      name: 'yixiaoer-open-user',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
