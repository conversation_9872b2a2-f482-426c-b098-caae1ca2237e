import { OnlineScaleRes } from '@/types/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { ChartConfig, ChartContainer } from '@mono/ui/chart';
import React from 'react';
import ReactECharts from 'echarts-for-react';

const chartConfig = {} satisfies ChartConfig;

export function AccountOnlineScale({
  data,
  platformName,
}: {
  data: OnlineScaleRes['stats'];
  platformName: string;
}) {
  const chartData = React.useMemo(() => {
    return Object.entries(data || {}).map(([key, value]) => ({
      name: key,
      value: value,
    }));
  }, [data]);
  const totalVisitors = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.value, 0);
  }, [chartData]);

  const option = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '注册占比',
          type: 'pie',
          radius: ['40%', '70%'],
          data: chartData,
        },
      ],
    }),
    [chartData, totalVisitors]
  );

  return (
    <Card className="w-full">
      <CardHeader className="flex items-center flex-row justify-between py-0 h-[76px]">
        <CardTitle className="text-xl">{platformName}</CardTitle>
      </CardHeader>
      <CardContent>
        {totalVisitors === 0 ?
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            暂无数据
          </div>
        : <ChartContainer config={chartConfig} className="w-full">
            <ReactECharts option={option} className={'!w-full !h-full'} />
          </ChartContainer>
        }
      </CardContent>
    </Card>
  );
}
