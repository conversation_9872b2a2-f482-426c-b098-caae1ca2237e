import { OnlineScaleRes } from '@/types/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import React from 'react';
import { Pie, Label, PieChart, LabelList } from 'recharts';

// 动态生成 HSL 颜色值的函数
function generateDynamicHSL(index: number): string {
  const hue = (index * 30) % 360;
  if (index < 5) {
    return `hsl(var(--chart-${index + 1}))`;
  } // 每次增加 30° 来生成新的颜色
  return `hsl(${hue}, 70%, 60%)`; // 色调、饱和度、亮度
}

// const chartConfig = {
//   visitors: {
//     label: 'Visitors',
//     color: 'hsl(var(--chart-default))',
//   },
//   androidRegisterNum: {
//     label: '安卓',
//     color: 'hsl(var(--chart-1))',
//   },
//   iosRegisterNum: {
//     label: 'ios',
//     color: 'hsl(var(--chart-2))',
//   },
//   macRegisterNum: {
//     label: 'mac',
//     color: 'hsl(var(--chart-3))',
//   },
//   windowsRegisterNum: {
//     label: 'windows',
//     color: 'hsl(var(--chart-4))',
//   },
// } satisfies ChartConfig;

export function AccountOnlineScale({
  data,
  platformName,
}: {
  data: OnlineScaleRes['stats'];
  platformName: string;
}) {
  const [chartData, chartConfig] = React.useMemo(() => {
    if (data) {
      const chartConfig: Record<string, { label: string; color: string }> = {};
      const chartData: { browser: string; visitors: number; fill: string }[] =
        [];
      Object.entries(data).forEach(([key, value], index) => {
        chartConfig[key] = {
          label: key,
          color: generateDynamicHSL(index),
        };
        chartData.push({
          browser: key,
          visitors: value,
          fill: chartConfig[key as keyof typeof chartConfig].color,
        });
      });
      chartData.sort((a, b) => b.visitors - a.visitors);
      return [chartData, chartConfig];
    }
    return [[], {}];
  }, [data]);
  const totalVisitors = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.visitors, 0);
  }, [chartData]);

  return (
    <Card className="w-full">
      <CardHeader className="flex items-center flex-row justify-between py-0 h-[76px]">
        <CardTitle className="text-xl">{platformName}</CardTitle>
      </CardHeader>
      <CardContent>
        {totalVisitors === 0 ?
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            暂无数据
          </div>
        : <ChartContainer config={chartConfig} className="w-full">
            <PieChart>
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent hideLabel />}
              />
              <Pie
                data={chartData}
                dataKey="visitors"
                nameKey="browser"
                innerRadius={60}
                strokeWidth={5}
                labelLine={false}
                label={({ payload, ...props }) => {
                  if (payload.visitors === 0) return null; // 过滤掉访客数为 0 的数据
                  return (
                    <text
                      cx={props.cx}
                      cy={props.cy}
                      x={props.x}
                      y={props.y}
                      textAnchor={props.textAnchor}
                      dominantBaseline={props.dominantBaseline}
                    >
                      {payload.browser}
                    </text>
                  );
                }}
              >
                <Label
                  content={({ viewBox }) => {
                    if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                      return (
                        <text
                          x={viewBox.cx}
                          y={viewBox.cy}
                          textAnchor="middle"
                          dominantBaseline="middle"
                        >
                          <tspan
                            x={viewBox.cx}
                            y={viewBox.cy}
                            className="fill-foreground text-3xl font-bold"
                          >
                            {totalVisitors.toLocaleString()}
                          </tspan>
                          <tspan
                            x={viewBox.cx}
                            y={(viewBox.cy || 0) + 24}
                            className="fill-muted-foreground"
                          >
                            总账号数
                          </tspan>
                        </text>
                      );
                    }
                  }}
                />
                <LabelList
                  dataKey="visitors"
                  className="fill-background"
                  stroke="none"
                  fontSize={12}
                  formatter={(value: number) =>
                    value ? value.toLocaleString() : ''
                  }
                />
              </Pie>
            </PieChart>
          </ChartContainer>
        }
      </CardContent>
    </Card>
  );
}
