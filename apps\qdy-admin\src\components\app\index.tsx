import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { AppTableSearch } from './AppTableSearch';
import { AppPlatformType, AppVersion, AppVersionParams } from '@/types/app';
import { SetDelete } from './SetDelete';
import { AppVersionTable } from './AppVersionTable';
import { getAppVersionColumns } from './columns';
import { SetCreate } from './SetCreate';

export function AppPage() {
  const [searchParams, setSearchParams] = useState<AppVersionParams>({});

  const [open, setOpen] = useState(false);
  const [openCreate, setCreateOpen] = useState(false);
  const [selectAppVersion, setSelectAppVersion] = useState<
    AppVersion | undefined
  >();

  const columns = useMemo(
    () =>
      getAppVersionColumns((item) => {
        setSelectAppVersion(item);
        setOpen(true);
      }),
    []
  );

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <AppTableSearch
        onSearch={(value: AppVersionParams) => {
          const cValue = { ...value };
          if (cValue.type === AppPlatformType.All) {
            delete cValue.type;
          }
          setSearchParams(cValue);
        }}
        values={searchParams}
        onCreate={setCreateOpen}
      />
      <AppVersionTable
        params={searchParams}
        columns={columns as ColumnDef<AppVersion, unknown>[]}
      />
      <SetDelete open={open} setOpen={setOpen} item={selectAppVersion} />
      <SetCreate open={openCreate} setOpen={setCreateOpen} />
    </div>
  );
}
