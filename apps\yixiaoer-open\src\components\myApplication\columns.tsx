import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import {
  Application,
  applicationTypeMap,
  appStatusMap,
  appStatusStyleMap,
} from '@/types/application';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';

export function getTableColumns(
  edit: (application: Application) => void,
  del: (id: string) => void,
  onDetail: (id: string) => void
): Array<ColumnDef<Application>> {
  return [
    {
      header: '应用名称',
      accessorKey: 'name',
      size: 200,
      cell: ({ row }: { row: { original: Application } }) => {
        const { id, name, status } = row.original;
        return (
          <span
            className={`${status === 1 ? 'cursor-pointer text-blue-500' : 'text-muted-foreground'}`}
            onClick={status === 1 ? () => onDetail(id) : undefined}
          >
            {name}
          </span>
        );
      },
    },
    {
      header: '应用ID',
      accessorKey: 'username',
      size: 250,
      cell: ({ row }) => {
        const { appId } = row.original;
        return <span className="text-muted-foreground">{appId}</span>;
      },
    },
    {
      header: '应用类型',
      accessorKey: 'applicationType',
      size: 250,
      cell: ({ row }) => {
        const { applicationType } = row.original;
        return (
          <span className="text-muted-foreground">
            {applicationTypeMap[applicationType]}
          </span>
        );
      },
    },
    {
      header: '蚁贝',
      accessorKey: '',
      size: 80,
      cell: ({ row }) => {
        const { availableBalance } = row.original;
        return (
          <span className="text-muted-foreground">{availableBalance}</span>
        );
      },
    },
    {
      header: '账号',
      accessorKey: 'account',
      size: 80,
      cell: ({ row }) => {
        const { totalAccountPoints } = row.original;
        return (
          <span className="text-muted-foreground">{totalAccountPoints}</span>
        );
      },
    },
    {
      header: '流量',
      accessorKey: 'internetTraffic',
      size: 100,
      cell: ({ row }) => {
        const { totalTraffic, usedTraffic } = row.original;
        return (
          <span className="text-muted-foreground">
            {usedTraffic}/{totalTraffic}
          </span>
        );
      },
    },
    {
      header: '状态',
      accessorKey: 'status',
      size: 100,
      cell: ({ row }) => {
        const { status } = row.original;
        return (
          <span className={`${appStatusStyleMap[status]}`}>
            {appStatusMap[status]}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      size: 80,
      cell: ({ row }) => {
        const { id } = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-haspopup="true"
                size="icon"
                variant="ghost"
                className="-ml-1"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {row.original.status === 1 && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDetail(id);
                  }}
                >
                  详情
                </DropdownMenuItem>
              )}
              {row.original.status !== 2 && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    edit(row.original);
                  }}
                >
                  编辑
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                className="text-destructive focus:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  del(id);
                }}
              >
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
