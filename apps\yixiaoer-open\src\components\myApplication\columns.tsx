import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Application, applicationTypeMap } from '@/types/application';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';

export function getTableColumns(
  edit: (application: Application) => void,
  del: (id: string) => void
): Array<ColumnDef<Application>> {
  return [
    {
      header: '应用名称',
      accessorKey: 'name',
      cell: ({ row }) => {
        const { name } = row.original;
        return <span className="text-muted-foreground">{name}</span>;
      },
    },
    {
      header: '应用ID',
      accessorKey: 'username',
      cell: ({ row }) => {
        const { appId } = row.original;
        return <span className="text-muted-foreground">{appId}</span>;
      },
    },
    {
      header: '类型',
      accessorKey: 'applicationType',
      cell: ({ row }) => {
        const { applicationType } = row.original;
        return (
          <span className="text-muted-foreground">
            {' '}
            {applicationTypeMap[applicationType]}
          </span>
        );
      },
    },
    {
      header: '蚁贝',
      accessorKey: '',
      cell: ({ row }) => {
        const { availableBalance } = row.original;
        return (
          <span className="text-muted-foreground">{availableBalance}</span>
        );
      },
    },
    {
      header: '账号',
      accessorKey: 'account',
      cell: ({ row }) => {
        const { totalAccountPoints } = row.original;
        return (
          <span className="text-muted-foreground">{totalAccountPoints}</span>
        );
      },
    },
    {
      header: '流量',
      accessorKey: 'internetTraffic',
      cell: ({ row }) => {
        const { totalTraffic, usedTraffic } = row.original;
        return (
          <span className="text-muted-foreground">
            {usedTraffic}/{totalTraffic}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const { id } = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-haspopup="true"
                size="icon"
                variant="ghost"
                className="-ml-1"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  edit(row.original);
                }}
              >
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:text-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  del(id);
                }}
              >
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
