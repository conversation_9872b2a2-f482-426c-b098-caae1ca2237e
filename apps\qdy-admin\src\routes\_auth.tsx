import { Nav } from '@/components/layout/Nav';
import { useUserStore } from '@/store/user';
import { Outlet, createFileRoute, redirect } from '@tanstack/react-router';

export const Route = createFileRoute('/_auth')({
  beforeLoad: ({ location }) => {
    if (!useUserStore.getState().userLogin) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: AuthLayout,
});

function AuthLayout() {
  return (
    <div className="grid h-full w-full grid-cols-[220px_1fr]">
      <Nav />
      <main className="grid flex-1 p-4 h-full overflow-hidden">
        <Outlet />
      </main>
    </div>
  );
}
