import { createFileRoute, redirect, Outlet } from '@tanstack/react-router';
import { useUserStore } from '@/store/user';
import { Nav } from '@/components/layout/nav';

export const Route = createFileRoute('/_home')({
  beforeLoad: ({ location }) => {
    if (!useUserStore.getState().userAuthorization) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: MainContentLayout,
});

function MainContentLayout() {
  return (
    <div className="grid h-full w-full grid-cols-[220px_1fr]">
      <Nav />
      <main className="grid flex-1 h-full p-3 overflow-hidden">
        <Outlet />
      </main>
    </div>
  );
}
