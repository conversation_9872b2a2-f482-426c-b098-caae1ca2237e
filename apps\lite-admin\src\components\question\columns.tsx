import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { QuestionDto } from '@/types/question';

export function getTableColumns(
  handleSetting: (data: QuestionDto, method: string) => void
): Array<ColumnDef<QuestionDto>> {
  return [
    {
      header: '序列号',
      accessorKey: 'sort',
      cell: ({ row }) => {
        const { sort } = row.original;
        return <span>{sort}</span>;
      },
    },
    {
      header: '标题',
      accessorKey: 'title',
      cell: ({ row }) => {
        const { title } = row.original;
        return <span>{title}</span>;
      },
    },
    {
      header: '跳转地址',
      accessorKey: 'questionUrl',
      cell: ({ row }) => {
        const { questionUrl } = row.original;
        return <span>{questionUrl ? questionUrl : '-'}</span>;
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const question = row.original;
        return (
          <>
            <Button
              variant="ghost"
              onClick={() => handleSetting(question, 'edit')}
            >
              编辑
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleSetting(question, 'delete')}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];
}
