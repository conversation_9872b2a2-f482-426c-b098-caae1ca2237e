import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { Minus, Plus } from 'lucide-react';

export function StepInput(props: {
  min: number;
  num: number;
  setNum: (num: number) => void;
  max: number;
}) {
  return (
    <div className="flex items-center gap-1">
      <Button
        type="button"
        size="icon"
        className="flex-shrink-0 h-8 w-8"
        variant="outline"
        disabled={props.num <= props.min}
        onClick={() => props.setNum(props.num - 1)}
      >
        <Minus className="w-4 h-4" />
      </Button>
      <Input
        min={1}
        step={1}
        max={9999}
        className="flex-1 h-8 bg-background text-center"
        type="text"
        value={props.num}
        onChange={(e) => {
          let inputValue = e.target.value;
          // 去掉小数点
          inputValue = inputValue.replace('.', '');
          if (/^[1-9]\d*$/.test(inputValue)) {
            const numericValue = Number(inputValue);
            if (numericValue < props.min) {
              props.setNum(props.min);
            } else if (numericValue > props.max) {
              props.setNum(props.max);
            } else {
              props.setNum(numericValue);
            }
          } else {
            props.setNum(props.min); // 如果输入不合法，则默认为1
          }
        }}
      />
      <Button
        type="button"
        disabled={props.num >= props.max}
        onClick={() => props.setNum(props.num + 1)}
        size="icon"
        className="flex-shrink-0 h-8 w-8"
        variant="outline"
      >
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
}
