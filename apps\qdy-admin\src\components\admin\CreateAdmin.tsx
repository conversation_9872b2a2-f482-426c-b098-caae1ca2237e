import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { LoadingButton } from '../loadingButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { createUser, updateUser } from '@/api/admin';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Admin, AdminDTO } from '@/types/admin';
import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';

// /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/
const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,15}$/;

const zodValidPassword = z
  .string()
  .min(1, { message: '请输入密码' })
  .regex(passwordRegex, {
    message: '密码格式不正确',
  });

const formSchema = z.object({
  nickname: z.string().min(1, '请输入姓名'),
  username: z.string().min(1, '请输入账号'),
  password: zodValidPassword,
});

export function CreateAdmin({
  open,
  setOpen,
  admin,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  admin: Admin | undefined;
}) {
  const queryClient = useQueryClient();
  const [show, setShow] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nickname: admin ? (admin.nickname ?? '') : '',
      username: admin ? (admin.username ?? '') : '',
      password: '',
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: admin ? updateUser : createUser,
    onSuccess: () => {
      completeSubmit();
    },
  });

  function completeSubmit() {
    setOpen(false);
    form.reset();
    queryClient.refetchQueries({
      queryKey: ['userList'],
    });
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.

    let data: AdminDTO = {
      nickname: '',
      password: '',
    };
    if (admin) {
      data = {
        userId: admin.id,
        nickname: values.nickname,
        username: values.username,
        password: values.password,
      };
    } else {
      data = {
        nickname: values.nickname,
        username: values.username,
        password: values.password,
      };
    }

    mutation.mutate(data);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="!max-w-none w-[500px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle className="text-center">
                {admin ? '编辑' : '创建'}管理员
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4 m-4">
              <FormField
                control={form.control}
                name="nickname"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-10 flex-shrink-0">姓名</FormLabel>
                      <FormControl>
                        <Input {...field} autoComplete="off" />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-10" />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-10 flex-shrink-0">账号</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          disabled={!!admin}
                          autoComplete="off"
                        />
                      </FormControl>
                    </FormItem>
                    <FormMessage className="ml-10" />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <div className="flex flex-col">
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-10 flex-shrink-0">密码</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <Input
                            type={show ? 'text' : 'password'}
                            {...field}
                            autoComplete="new-password"
                          />
                          <Button
                            onClick={() => setShow(!show)}
                            size="sm"
                            type="button"
                            variant="ghost"
                            className="absolute right-3 top-1/2 -translate-y-1/2"
                          >
                            {show ?
                              <Eye className="h-5 w-5" />
                            : <EyeOff className="h-5 w-5" />}
                          </Button>
                        </div>
                      </FormControl>
                    </FormItem>
                    <span className="text-[#B2B2B2] text-xs ml-10 mt-0.5">
                      必须同时包含英文大小写、符号、数字（8到15位字符）
                    </span>
                    <FormMessage className="ml-10" />
                  </div>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                提交
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
