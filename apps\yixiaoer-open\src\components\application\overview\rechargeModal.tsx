import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';

interface RechargeModalProps {
  open: boolean;
  onClose: () => void;
}

export function RechargeModal({ open, onClose }: RechargeModalProps) {
  // 固定的二维码地址
  const qrCodeUrl =
    'https://via.placeholder.com/200x200/4F46E5/FFFFFF?text=QR+Code';

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-medium text-[#222222] text-center">
            充值
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-6 py-4">
          {/* 二维码区域 */}
          <div className="flex flex-col items-center space-y-4">
            <div className="w-48 h-48 bg-[#F8F8FA] border border-[#E8EAEC] rounded-lg flex items-center justify-center">
              <img src={qrCodeUrl} alt="充值" className="w-40 h-40" />
            </div>

            <div className="text-center space-y-2">
              <p className="text-[#222222] font-medium">
                请微信扫码联系客户代表
              </p>
              <p className="text-sm text-[#757575]">为您充值</p>
            </div>
          </div>

          {/* 关闭按钮 */}
          <Button
            onClick={onClose}
            className="w-full bg-[#4F46E5] hover:bg-[#4338CA] text-white"
          >
            我知道了
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
