import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';
import { LoadingButton } from '../loadingButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Application, ApplicationDTO } from '@/types/application';
import { createApplication, updateApplication } from '@/api/application';
import LengthInput from '../lengthInput';
import { useEffect } from 'react';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group'; // Ensure this is the correct path for RadioGroup
import { Label } from '@mono/ui/label';

const formSchema = z.object({
  name: z.string().min(1, '请输入应用名称'),
  applicationType: z.enum(['TECHNICAL_SERVICE', 'CHANNEL_PARTNER'], {
    required_error: '请选择应用类型',
  }),
});

export function CreateOrEditApplication({
  open,
  setOpen,
  application,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  application: Application | undefined;
}) {
  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      applicationType: 'TECHNICAL_SERVICE',
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: application ? updateApplication : createApplication,
    onSuccess: () => {
      completeSubmit();
    },
  });

  function completeSubmit() {
    setOpen(false);
    form.reset();
    queryClient.refetchQueries({
      queryKey: ['applicationList'],
    });
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    let data: ApplicationDTO = {
      name: values.name,
      applicationType: values.applicationType,
    };

    if (application) {
      data = {
        id: application.id,
        ...data,
      };
    }

    mutation.mutate(data);
  }

  useEffect(() => {
    if (open) {
      form.setValue('name', application ? (application.name ?? '') : '');
      form.setValue(
        'applicationType',
        application ? application.applicationType : 'TECHNICAL_SERVICE'
      );
    }
  }, [application, form, open]);

  function handleApplicationTypeChange(
    value: 'TECHNICAL_SERVICE' | 'CHANNEL_PARTNER'
  ) {
    form.setValue('applicationType', value);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="!max-w-none w-[579px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{application ? '编辑' : '创建'}应用</DialogTitle>
            </DialogHeader>

            <div className="flex flex-col gap-4 m-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <div className="flex flex-col gap-2">
                    <FormItem className="space-y-2">
                      <FormLabel>应用名称</FormLabel>
                      <FormControl>
                        <LengthInput
                          maxLength={10}
                          minLength={1}
                          id="name"
                          value={field.value}
                          className="h-9"
                          onChange={(e) => field.onChange(e.target.value)}
                          placeholder="请输入应用名称"
                          autoComplete="off"
                        />
                      </FormControl>
                    </FormItem>
                    <FormMessage />
                  </div>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="applicationType"
              render={({ field }) => (
                <div className="flex flex-col gap-4 m-4">
                  <FormItem className="flex items-center gap-14 space-y-0">
                    <FormLabel className="flex-shrink-0">应用类型 *</FormLabel>
                    <FormControl>
                      <RadioGroup
                        disabled={!!application}
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          handleApplicationTypeChange(
                            value as 'TECHNICAL_SERVICE' | 'CHANNEL_PARTNER'
                          );
                        }}
                        className="flex gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="TECHNICAL_SERVICE"
                            id="technical"
                          />
                          <Label htmlFor="technical">Apass</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="CHANNEL_PARTNER"
                            id="channel"
                          />
                          <Label htmlFor="channel">渠道商</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                  <FormMessage className="ml-28" />
                </div>
              )}
            />

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                {application ? '更新' : '创建'}
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
