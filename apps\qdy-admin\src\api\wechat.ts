import { makeRequest } from '@/api/index.ts';
import {
  LoginRecord,
  OfflineCount,
  OnlineCount,
  Wechat,
  WechatSearchParams,
} from '@/types/wechat.ts';
import { PageData, PageQuery } from '@/types';

/**
 * 获取在线账号数量
 */
export const getOnlineCount = () => {
  return makeRequest<OnlineCount>({
    url: '/wechat/onlineCount',
    method: 'GET',
  });
};

/**
 * 获取离线账号数量
 */
export const getOfflineCount = (startDate: string) => {
  return makeRequest<OfflineCount>({
    url: '/wechat/offlineCount',
    method: 'GET',
    params: {
      startDate,
    },
  });
};

export const getWechat = (params: PageQuery & WechatSearchParams) => {
  return makeRequest<PageData<Wechat>>({
    url: '/wechat',
    method: 'GET',
    params,
  });
};

export const getHistoryLogin = (params: PageQuery, wxid: string) => {
  return makeRequest<PageData<LoginRecord>>({
    url: '/wechat/loginRecord',
    method: 'GET',
    params: {
      ...params,
      wxid,
    },
  });
};
