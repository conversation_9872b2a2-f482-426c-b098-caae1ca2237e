import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { SetAppPriceModal } from './SetAppPriceModal';
import { getAppColumns } from './AppColumns';
import { getUserApps } from '@/api/openPlatform';
import { OpenPlatformApp } from '@/types/openPlatform';
import { toast } from 'sonner';
import { useNavigate } from '@tanstack/react-router';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@mono/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';

interface AppManagementProps {
  userId: string;
}

export function AppManagement({ userId }: AppManagementProps) {
  const navigate = useNavigate();

  const [selectedApp, setSelectedApp] = useState<OpenPlatformApp | null>(null);
  const [setPriceModalOpen, setSetPriceModalOpen] = useState(false);

  // 获取应用列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['userApps', userId],
    queryFn: () =>
      getUserApps({
        userId,
      }),
  });

  // 查看应用数据
  const handleViewData = (app: OpenPlatformApp) => {
    // TODO: 实现查看应用数据功能
    toast.info('查看应用数据功能待实现');
    console.log('查看应用数据:', app);
  };

  // 设置应用价格
  const handleSetPrice = (app: OpenPlatformApp) => {
    setSelectedApp(app);
    setSetPriceModalOpen(true);
  };

  // 关闭设置价格弹窗
  const handleClosePriceModal = () => {
    setSetPriceModalOpen(false);
    setSelectedApp(null);
  };

  // 返回用户列表
  const handleGoBack = () => {
    navigate({ to: '/open-platform/users' });
  };

  const columns = getAppColumns({
    onViewData: handleViewData,
    onSetPrice: handleSetPrice,
  });

  // 创建表格实例
  const table = useReactTable({
    data: data?.applications || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : '未知错误'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {data?.userPhone || '用户'} 的应用列表
          </h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>应用列表</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ?
            <div className="flex items-center justify-center h-32">
              <div className="text-muted-foreground">加载中...</div>
            </div>
          : <div className="border rounded-md">
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id}>
                          {header.isPlaceholder ? null : (
                            flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )
                          )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ?
                    table.getRowModel().rows.map((row) => (
                      <TableRow key={row.id}>
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  : <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        暂无数据
                      </TableCell>
                    </TableRow>
                  }
                </TableBody>
              </Table>
            </div>
          }
        </CardContent>
      </Card>

      <SetAppPriceModal
        open={setPriceModalOpen}
        onClose={handleClosePriceModal}
        app={selectedApp}
      />
    </div>
  );
}
