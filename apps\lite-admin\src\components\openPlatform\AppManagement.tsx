import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { DataTable } from '@/components/dataTable';
import { AppSearch } from './AppSearch';
import { SetAppPriceModal } from './SetAppPriceModal';
import { getAppColumns } from './AppColumns';
import { getUserApps, getOpenPlatformUserDetail } from '@/api/openPlatform';
import { OpenPlatformApp, OpenPlatformAppParams } from '@/types/openPlatform';
import { toast } from 'sonner';
import { useNavigate } from '@tanstack/react-router';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@mono/ui/button';

interface AppManagementProps {
  userId: string;
}

export function AppManagement({ userId }: AppManagementProps) {
  const navigate = useNavigate();

  // 调试信息
  console.log('AppManagement 组件已渲染，userId:', userId);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const [searchParams, setSearchParams] = useState<Omit<OpenPlatformAppParams, 'userId'>>({});
  const [selectedApp, setSelectedApp] = useState<OpenPlatformApp | null>(null);
  const [setPriceModalOpen, setSetPriceModalOpen] = useState(false);

  // 获取用户信息
  const { data: userInfo } = useQuery({
    queryKey: ['openPlatformUser', userId],
    queryFn: () => getOpenPlatformUserDetail(userId),
  });

  // 获取应用列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['userApps', userId, pagination, searchParams],
    queryFn: () =>
      getUserApps({
        userId,
        ...searchParams,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  // 处理搜索
  const handleSearch = (params: Omit<OpenPlatformAppParams, 'userId'>) => {
    setSearchParams(params);
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  // 查看应用数据
  const handleViewData = (app: OpenPlatformApp) => {
    // TODO: 实现查看应用数据功能
    toast.info('查看应用数据功能待实现');
    console.log('查看应用数据:', app);
  };

  // 设置应用价格
  const handleSetPrice = (app: OpenPlatformApp) => {
    setSelectedApp(app);
    setSetPriceModalOpen(true);
  };

  // 查看订单（跳转到订单列表并筛选该应用）
  const handleViewOrders = (app: OpenPlatformApp) => {
    // 使用 window.location 进行跳转，传递 appId 参数
    window.location.href = `/open-platform/orders?appId=${app.appId}`;
  };

  // 关闭设置价格弹窗
  const handleClosePriceModal = () => {
    setSetPriceModalOpen(false);
    setSelectedApp(null);
  };

  // 返回用户列表
  const handleGoBack = () => {
    navigate({ to: '/open-platform/users' });
  };

  const columns = getAppColumns({
    onViewData: handleViewData,
    onSetPrice: handleSetPrice,
    onViewOrders: handleViewOrders,
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : '未知错误'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleGoBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {userInfo?.phone || '用户'} 的应用列表
          </h1>
          <p className="text-muted-foreground">
            管理用户的应用信息和价格设置
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>搜索筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <AppSearch onSearch={handleSearch} values={searchParams} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>应用列表</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={isLoading ? undefined : data?.data || []}
            rowCount={data?.total || 0}
            pagination={pagination}
            setPagination={setPagination}
          />
        </CardContent>
      </Card>

      <SetAppPriceModal
        open={setPriceModalOpen}
        onClose={handleClosePriceModal}
        app={selectedApp}
      />
    </div>
  );
}
