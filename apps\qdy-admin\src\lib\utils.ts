import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isPhoneNumber(phone: string): boolean {
  // 正则表达式，匹配以1开头的11位数字
  const pattern = /^1\d{10}$/;
  return pattern.test(phone);
}

// 保留两位小数，不满两位补零
export function formatPrice(price: number) {
  const absPrice = Math.abs(price);
  const sign = price < 0 ? '-' : '';
  return sign + absPrice.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 月份格式化 12个月为1年
export function formatMount(mount: number) {
  const year = Math.floor(mount / 12);
  const month = mount % 12;
  return year ? `${year}年` + (month ? `${month}个月` : '') : `${month}个月`;
}
