import { ColumnDef } from '@tanstack/react-table';
import { Admin } from '@/types/admin';
import { formatDate } from '@/lib/utils/day';
import { Button } from '@mono/ui/button';

export function getTableColumns(
  currentRole: number,
  currentUsername: string,
  edit: (admin: Admin) => void,
  del: (adminId: string) => void
): Array<ColumnDef<Admin>> {
  return [
    {
      header: '姓名',
      accessorKey: 'nickname',
      size: 250,
      cell: ({ row }) => {
        const { nickname } = row.original;
        return <span className="text-muted-foreground">{nickname}</span>;
      },
    },
    {
      header: '账号',
      accessorKey: 'username',
      cell: ({ row }) => {
        const { username } = row.original;
        return <span className="text-muted-foreground">{username}</span>;
      },
    },
    {
      header: '创建时间',
      accessorKey: 'registerTime',
      cell: ({ row }) => {
        const { createTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatDate(createTime)}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const { id, username } = row.original;
        return (
          <>
            {currentRole === 0 && (
              <div className="flex">
                <Button variant="ghost" onClick={() => edit(row.original)}>
                  编辑
                </Button>
                {currentUsername !== username && (
                  <Button variant="ghost" onClick={() => del(id)}>
                    删除
                  </Button>
                )}
              </div>
            )}
          </>
        );
      },
    },
  ];
}
