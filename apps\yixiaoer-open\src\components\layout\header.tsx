import { Button } from '@mono/ui/button';
import { Profile } from './profile';
import { Link } from '@tanstack/react-router';
import NoticeIcon from '@/assets/notice.svg';

export function Header() {
  return (
    <div className="flex items-center justify-between h-16 pl-4 pr-5 shadow-[0px_1px_6px_0px_rgba(0,0,0,0.04)]">
      <div className="flex items-center gap-3">
        <Profile />

        <Button variant="ghost" className="p-0 h-max hover:bg-transparent">
          <Link to="/console-board">
            <span className="text-sm text-[#222222] font-medium">控制台</span>
          </Link>
        </Button>
      </div>

      <Button size="icon" variant="ghost">
        <img src={NoticeIcon} alt="" className="" />
      </Button>
    </div>
  );
}
