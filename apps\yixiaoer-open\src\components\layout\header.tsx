import { Button } from '@mono/ui/button';
import { Profile } from './profile';
import { Link } from '@tanstack/react-router';
import Logo from '@/assets/20250521-171103.png';

export function Header() {
  return (
    <div className="flex items-center justify-between h-16 pl-4 pr-5 shadow-[0px_1px_6px_0px_rgba(0,0,0,0.04)]">
      {/* 左侧 Logo */}
      <div className="flex items-center gap-3">
        <img src={Logo} alt="Logo" className="h-10 w-10" />
        <span className="text-lg font-semibold text-[#222222]">
          蚁小二开放平台
        </span>
      </div>

      {/* 右侧控制台和头像 */}
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          className="p-0 h-max hover:bg-transparent mr-[10px]"
        >
          <Link to="/console-board">
            <span className="text-sm text-[#222222] font-medium">控制台</span>
          </Link>
        </Button>

        <Profile />
      </div>
    </div>
  );
}
