import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { ActivationRecordParams } from '@/types/coupon';

const formSchema = z.object({
  phone: z.string().optional(),
  teamName: z.string().optional(),
  postTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  activeTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  status: z.string().optional(),
});

export function ActivationRecordTableSearch({
  values,
  onSearch,
}: {
  values: ActivationRecordParams;
  onSearch: (values: ActivationRecordParams) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: values.status?.toString(),
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: ActivationRecordParams = {
      phone: values.phone?.trim(),
      teamName: values.teamName?.trim(),
      createTimeStart: values.postTimeRange?.from?.getTime(),
      createTimeEnd: values.postTimeRange?.to?.getTime(),
      castTimeStart: values.activeTimeRange?.from?.getTime(),
      castTimeEnd: values.activeTimeRange?.to?.getTime(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    if (values.status) {
      paramsOmitEmpty['status'] = Number(values.status);
    }

    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 justify-start p-0.5"
      >
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入手机号搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入团队名称搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="postTimeRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">发放时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="activeTimeRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>使用时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>使用状态</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={`${field.value}`}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="-1">全部</SelectItem>
                  <SelectItem value="1">已使用</SelectItem>
                  <SelectItem value="0">未使用</SelectItem>
                  <SelectItem value="2">已过期</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
