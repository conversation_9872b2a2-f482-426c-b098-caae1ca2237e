import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { omitBy } from 'lodash';
import { OrderParams } from '@/types/order';
import { resourceTypeMap } from '@/config/order';
import { CalendarRange } from '@/components/CalendarRange';

export const numberRegex = /^[1-9]\d*$/;

const formSchema = z.object({
  orderNo: z.string().optional(),
  code: z.string().optional(),
  payTime: z.date().optional(),
  resourceType: z.string().optional(),
  payDateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .optional(),
});

export function TableSearch({
  values,
  onSearch: onSearch,
}: {
  values: OrderParams;
  onSearch: (values: OrderParams) => void;
}) {
  const { setValue, ...form } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      orderNo: values.orderNo,
      code: values.code,
      resourceType: values.resourceType || 'all',
      // payDateRange 默认为 undefined，确保初始状态为空
      payDateRange: undefined,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: OrderParams = {
      orderNo: values.orderNo,
      code: values.code?.trim(),
      // 只有当日期范围存在且有有效的开始和结束日期时才传递时间戳
      paymentStartTime:
        values.payDateRange?.from ?
          values.payDateRange.from.getTime()
        : undefined,
      paymentEndTime:
        values.payDateRange?.to ? values.payDateRange.to.getTime() : undefined,
      resourceType:
        values.resourceType === 'all' ? undefined : values.resourceType,
    };
    // 过滤掉 undefined、null、空字符串，但保留 0 值（因为时间戳可能为0）
    const paramsOmitEmpty = omitBy(
      params,
      (value) => value === undefined || value === null || value === ''
    );
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form setValue={setValue} {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 p-0.5"
      >
        <FormField
          control={form.control}
          name="orderNo"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入订单号搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队ID</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入团队ID搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="payDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>支付时间</FormLabel>
              <CalendarRange
                value={
                  field.value && field.value.from ?
                    {
                      from: field.value.from,
                      to: field.value.to,
                    }
                  : undefined
                }
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="resourceType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>订单类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {Object.keys(resourceTypeMap).map((key) => {
                    return (
                      <SelectItem key={key} value={key}>
                        {resourceTypeMap[key as keyof typeof resourceTypeMap]}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />

        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
