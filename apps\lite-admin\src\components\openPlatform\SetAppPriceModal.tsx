import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { setAppPrice } from '@/api/openPlatform';
import { OpenPlatformApp } from '@/types/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  accountPrice: z.number().min(0, '账号价格不能小于0'),
  trafficPrice: z.number().min(0, '流量价格不能小于0'),
  packagePrice: z.number().min(0, '流量价格不能小于0'),
});

type FormData = z.infer<typeof formSchema>;

interface SetAppPriceModalProps {
  open: boolean;
  onClose: () => void;
  app: OpenPlatformApp | null;
}

export function SetAppPriceModal({
  open,
  onClose,
  app,
}: SetAppPriceModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountPrice: app?.accountPrice || 0,
      trafficPrice: app?.trafficPrice || 0,
      packagePrice: app?.packagePrice || 0,
    },
  });

  // 当应用数据变化时重置表单
  useEffect(() => {
    if (app) {
      form.reset({
        accountPrice: app.accountPrice || 0,
        trafficPrice: app.trafficPrice || 0,
        packagePrice: app.packagePrice || 0,
      });
    }
  });

  const mutation = useMutation({
    mutationFn: setAppPrice,
    onSuccess: () => {
      toast.success('应用价格设置成功');
      queryClient.invalidateQueries({ queryKey: ['userApps'] });
      onClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '应用价格设置失败';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    if (!app) return;

    mutation.mutate({
      id: app.id,
      accountPrice: data.accountPrice,
      trafficPrice: data.trafficPrice,
      packagePrice: data.packagePrice,
    });
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置应用价格</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                应用：{app?.name} ({app?.appId})
              </div>

              <FormField
                control={form.control}
                name="accountPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号单价</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="请输入账号单价"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trafficPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>流量单价</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="请输入流量单价"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="packagePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>权益包单价</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="请输入权益包单价"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                保存
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
