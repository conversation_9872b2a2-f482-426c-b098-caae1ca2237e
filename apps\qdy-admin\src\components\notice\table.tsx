import TableData from '@/components/table.tsx';
import { usePageQuery } from '@/hooks/usePageQuery.ts';
import { getNotices } from '@/api/notice.ts';
import { ColumnDef } from '@tanstack/react-table';
import { Notify } from '@/types/notice.ts';

export function TableBase({
  columns,
}: {
  columns: ColumnDef<Notify, string>[];
}) {
  const { query, pagination, setPagination } = usePageQuery(
    ['getNotices'],
    getNotices
  );
  return (
    <TableData
      columns={columns}
      data={query.data?.data}
      rowCount={query.data?.total ?? 0}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}
