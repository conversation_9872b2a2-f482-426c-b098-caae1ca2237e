import { getPublishScale } from '@/api/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { ChartConfig, ChartContainer } from '@mono/ui/chart';
import { useQuery } from '@tanstack/react-query';
import React from 'react';

import ReactECharts from 'echarts-for-react';

const chartConfig = {
  successPublishTotal: {
    label: '发布成功',
    color: 'hsl(var(--chart-1))',
  },
  failPublishTotal: {
    label: '发布失败',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig;

export function Publish() {
  const { data } = useQuery({
    queryKey: ['getPublishScale'],
    queryFn: getPublishScale,
  });

  const chartData = React.useMemo(() => {
    if (data) {
      return data
        .map(
          ({
            platformName: platformName,
            successPublishTotal: successPublishTotal,
            failPublishTotal: failPublishTotal,
          }) => ({
            platformName,
            successPublishTotal,
            failPublishTotal,
          })
        )
        .sort(
          (a, b) =>
            b.successPublishTotal +
            b.failPublishTotal -
            (a.successPublishTotal + a.failPublishTotal)
        );
    }
    return [];
  }, [data]);
  const { publishSum, successPublishSum, failPublishSum } =
    React.useMemo(() => {
      return {
        publishSum: chartData.reduce(
          (acc, curr) => acc + curr.successPublishTotal + curr.failPublishTotal,
          0
        ),
        successPublishSum: chartData.reduce(
          (acc, curr) => acc + curr.successPublishTotal,
          0
        ),
        failPublishSum: chartData.reduce(
          (acc, curr) => acc + curr.failPublishTotal,
          0
        ),
      };
    }, [chartData]);

  const option = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {},
      grid: {
        left: '0%',
        right: '0%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: [...chartData.map(({ platformName }) => platformName)],
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          name: '发布成功',
          type: 'bar',
          data: [
            ...chartData.map(({ successPublishTotal }) => successPublishTotal),
          ],
          itemStyle: {
            color: '#4CAF50', // ✅ 绿色（在线）
          },
          label: {
            show: true, // ✅ 开启标签
            position: 'top', // ✅ 显示在柱子顶端
            color: '#4CAF50', // 可选：绿色文字，与柱子一致
            fontWeight: 'bold',
          },
        },
        {
          name: '发布失败',
          type: 'bar',
          stack: 'Ad',
          data: [...chartData.map(({ failPublishTotal }) => failPublishTotal)],
          itemStyle: {
            color: '#B0BEC5',
          },
          label: {
            show: true,
            position: 'top',
            color: '#B0BEC5',
            fontWeight: 'bold',
          },
        },
      ],
    }),
    [chartData]
  );

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="">
        <CardTitle className="text-xl">
          近7天发布(总数:{publishSum}，成功数:{successPublishSum}，失败数:
          {failPublishSum})
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <ReactECharts option={option} className="!w-full !h-full" />
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
