import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { omitBy } from 'lodash';
import { ChannelReq } from '@/types/channel';

export function TableSearch({
  values,
  onSearch,
}: {
  values?: ChannelReq;
  onSearch: (values: ChannelReq) => void;
}) {
  const form = useForm<ChannelReq>({
    defaultValues: values,
  });

  function onSubmit(values: ChannelReq) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: ChannelReq = {
      channelName: values.channelName?.trim(),

      channelCode: values.channelCode?.trim(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="channelName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道名称</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索渠道" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="channelCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道码</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索渠道码" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
