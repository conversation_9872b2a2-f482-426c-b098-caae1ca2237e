import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { LoadingButton } from '@/components/loading-button';
import { useEffect, useMemo, useRef } from 'react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { addDesktopConfig, setDesktopConfig } from '@/api/desktop';
import { DesktopDetail, OS } from '@/types/desktopVersion';
import { Textarea } from '@mono/ui/textarea';

export function DesktopSetting({
  desktopType,
  desktop,
  open,
  setOpen,
  latest,
}: {
  desktopType: OS;
  desktop?: DesktopDetail;
  open: boolean;
  setOpen: (open: boolean) => void;
  latest: string;
}) {
  const queryClient = useQueryClient();

  const formSchema = useMemo(() => {
    return z.object({
      id: z.string().optional(),
      version: z.string(),
      requestUrl: z.string().optional(),
      isForce: z.boolean(),
      notice: z.string(),
      scripts: z.string().optional(),
    });
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      version: '',
      isForce: false,
      notice: '',
      scripts: '',
    },
  });

  const fileRef = useRef<File>();

  useEffect(() => {
    if (open && desktop) {
      form.setValue('id', desktop.id);
      form.setValue('requestUrl', desktop.requestUrl);
      form.setValue('version', desktop.version);
      form.setValue('notice', desktop.notice);
      form.setValue('isForce', desktop.isForce);
    }
  }, [form, desktop, open]);

  const mutation = useMutation({
    mutationFn: addDesktopConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['desktopList'],
      });
    },
  });

  const editMutation = useMutation({
    mutationFn: setDesktopConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['desktopList'],
      });
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }
    fileRef.current = file;
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (values.id) {
      editMutation.mutate({
        id: values.id,
        type: desktopType,
        version: values.version,
        isForce: values.isForce,
        notice: values.notice,
        file: fileRef.current,
      });
    } else {
      mutation.mutate({
        type: desktopType,
        isForce: values.isForce,
        version: values.version,
        notice: values.notice,
        file: fileRef.current,
        requestUrl: values.requestUrl,
      });
    }
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>
                {desktop?.id ? '修改' : '添加'}
                {desktopType.toLocaleUpperCase()}版本
              </DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="version"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-25 flex-shrink-0 text-right mr-2">
                      版本号:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="flex-grow"
                        placeholder="填写版本号如:1.0.0"
                        disabled={!!desktop?.id}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <span className={'text-sm pl-[78px]'}>当前版本：{latest}</span>
              <FormField
                control={form.control}
                name="isForce"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-25 flex-shrink-0 text-right mr-2">
                      强制更新:
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={String(field.value)}
                        onValueChange={(value) =>
                          field.onChange(value === 'true')
                        }
                        className="flex flex-row space-x-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">否</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">是</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              {desktopType == OS.Android && (
                <FormField
                  control={form.control}
                  name="scripts"
                  render={() => (
                    <FormItem className="flex items-center gap-1 mb-4 space-y-0">
                      <FormLabel className="w-25 flex-shrink-0 text-right mr-2">
                        安装包文件:
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept={'.apk'}
                          className="flex-grow"
                          onChange={(e) => handleFileChange(e)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="notice"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mt-4 space-y-0">
                    <FormLabel className="w-25 flex-shrink-0 text-right mr-2">
                      版本公告:
                    </FormLabel>
                    <FormControl>
                      <Textarea placeholder="填写公告" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
