import { DataTable } from '@/components/dataTable';
import { getTableColumns } from './columns';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { TableSearch } from './TableSearch';
import { platformAccountList } from '@/api/platformAccount';
import { PlatformAccountParams } from '@/types/platformAccount';
import { useSearch } from '@tanstack/react-router';

export function PlatformAccountPage() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const { createStartTime, createEndTime } = useSearch({
    strict: false,
  });
  const [filter, setFilter] = useState<PlatformAccountParams>({
    createStartTime: createStartTime ?? undefined,
    createEndTime: createEndTime ?? undefined,
    teamName: undefined,
  });

  const query = useQuery({
    queryKey: ['platformAccountList', pagination, filter],
    queryFn: () =>
      platformAccountList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });

  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  return (
    <div className="flex flex-col gap-2 overflow-hidden">
      <TableSearch
        values={filter}
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 });
          setFilter(value);
        }}
      />

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
