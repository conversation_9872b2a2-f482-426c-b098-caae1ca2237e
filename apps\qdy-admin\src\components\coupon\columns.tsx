import { createColumnHelper } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day.ts';
import { Coupon } from '@/types/coupon';
import DropdownMenuComponent from '../DropdownMenuComponent';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';
import { Button } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';

const columnHelper = createColumnHelper<Coupon>();

export const getColumns = (
  onChange: (item: Coupon, type: 'update' | 'del' | 'send') => void
) => {
  return [
    columnHelper.accessor('name', {
      cell: (info) => info.getValue(),
      header: () => <span>满减券名称</span>,
    }),
    columnHelper.accessor('id', {
      cell: (info) => info.getValue(),
      header: () => <span>优惠券ID</span>,
    }),
    columnHelper.accessor('minimumSpendingAmount', {
      cell: (info) => (
        <span>
          {info.getValue()}-{info.row.original.discountAmount}
        </span>
      ),
      header: () => <span>满减金额</span>,
    }),
    columnHelper.accessor('creatorName', {
      cell: (info) => info.getValue(),
      header: () => <span>创建人</span>,
    }),
    columnHelper.accessor('createTime', {
      header: () => '创建时间',
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
    }),
    columnHelper.accessor('activeAmount', {
      cell: (info) =>
        info.getValue() >= 0 ?
          <Button variant="link" className="px-0 h-4 text-[#3369D6]">
            <Link
              to="/activation-record"
              search={{ couponId: info.row.original.id }}
            >
              <span>{info.row.original.postAmount}</span>/{info.getValue()}
            </Link>
          </Button>
        : <span>
            {info.row.original.postAmount}/{info.getValue()}
          </span>,
      header: () => <span>发放/激活</span>,
    }),
    columnHelper.accessor('postAmount', {
      header: () => '操作',
      cell: (info) => {
        return (
          <DropdownMenuComponent>
            <>
              <DropdownMenuItem
                onClick={() => onChange(info.row.original, 'send')}
              >
                发放
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onChange(info.row.original, 'update')}
              >
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:text-destructive"
                onClick={() => onChange(info.row.original, 'del')}
              >
                删除
              </DropdownMenuItem>
            </>
          </DropdownMenuComponent>
        );
      },
    }),
  ];
};
