# 编译源码
FROM node:20-buster-slim AS build
ARG PACKAGE_SCRIPT=build
ARG CI_APP_PATH
ARG APP_NAME
WORKDIR /src
COPY . .
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm
RUN pnpm install
RUN pnpm run --filter $APP_NAME $PACKAGE_SCRIPT

# ARG MIRROR_URL=https://atomhub.openatom.cn/
# 运行环境
FROM nginx:stable AS runner
ARG CI_APP_PATH
WORKDIR /usr/share/nginx/html
COPY --from=build /src/$CI_APP_PATH/dist .
COPY nginx.conf /etc/nginx/conf.d/default.conf
ENV TZ=Asia/Shanghai
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
