'use client';

import * as React from 'react';
import { CheckIcon } from '@radix-ui/react-icons';

import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@mono/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { debounce } from 'lodash';
import { LoadingContainer } from '@/components/loading.tsx';
import { getCoupon } from '@/api/coupon';

export const SelectCoupon = React.forwardRef<
  React.ElementRef<typeof PopoverContent>,
  {
    value?: string;
    onChange: (value?: string) => void;
  }
>(({ value, onChange }, ref) => {
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState('');
  const query = useQuery({
    queryFn: () => getCoupon(searchText),
    queryKey: ['getCoupon', searchText],
    enabled: !!searchText,
  });

  // TODO: 优化搜索
  const searchFun = useMemo(
    () => debounce(setSearchText, 300),
    [setSearchText]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-start">
          {`${query.data?.id}` === value ?
            <>{query.data?.name}</>
          : <>选择优惠券</>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[462px]" ref={ref}>
        <Command shouldFilter={false}>
          <CommandInput
            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
              searchFun(e.target.value);
            }}
            placeholder="请输入优惠券ID搜索"
            className="h-9"
          />
          <CommandList>
            {query.isLoading ?
              <div className="py-6">
                <LoadingContainer className="w-5 h-5" />
              </div>
            : <CommandEmpty>No teams found</CommandEmpty>}
            <CommandGroup>
              {query.data &&
                [query.data].map((team) => (
                  <CommandItem
                    // disabled={team.hasPendingOrder || team.hasVip}
                    key={team.id}
                    value={`${team.id}`}
                    className="w-full flex items-center gap-2 justify-between"
                    onSelect={(currentValue) => {
                      onChange(currentValue === value ? '' : currentValue);
                      setOpen(false);
                    }}
                  >
                    <span>{team.name}</span>
                    <CheckIcon
                      className={cn(
                        'ml-auto h-4 w-4',
                        value === `${team.id}` ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
});
