import { getOnlineScale } from '@/api/data';
import { ScrollArea } from '@mono/ui/scroll-area';
import { useQuery } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';
import { AccountOnlineScale } from './accountOnline';
import { Switch } from '@mono/ui/switch';
import { useState } from 'react';

export const OnlineStatisticsPage = () => {
  const [showExpired, setShowExpired] = useState(false);
  const { data, isLoading } = useQuery({
    queryKey: ['getOnlineScale', showExpired],
    queryFn: () => getOnlineScale(showExpired),
  });
  if (isLoading) return <LoadingContainer />;
  return (
    <ScrollArea className="h-full">
      <div className="mb-4 flex items-center gap-2">
        <Switch checked={showExpired} onCheckedChange={setShowExpired} />
        <span>显示已失效账号</span>
      </div>
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4">
        {data?.map((x) => (
          <AccountOnlineScale data={x.stats} platformName={x.platformName} />
        ))}
      </div>
    </ScrollArea>
  );
};
