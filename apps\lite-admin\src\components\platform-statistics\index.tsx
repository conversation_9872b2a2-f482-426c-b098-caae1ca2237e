import { getAccountStatistic } from '@/api/data';
import { ScrollArea } from '@mono/ui/scroll-area';
import { useQuery } from '@tanstack/react-query';
import { LoadingContainer } from '../loading';
import { AreaChartComponent } from '../overviews/AreaChart';

export const PlatformStatisticsPage = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['getAccountStatistic'],
    queryFn: getAccountStatistic,
  });
  if (isLoading) return <LoadingContainer />;
  return (
    <ScrollArea className="h-full">
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3 3xl:grid-cols-4">
        {data?.map((x) => (
          <AreaChartComponent
            title={x.platformName}
            chartData={
              x.trendData.map((item) => ({
                date: item.date,
                value: item.incrementAccountTotal,
              })) ?? []
            }
          />
        ))}
      </div>
    </ScrollArea>
  );
};
