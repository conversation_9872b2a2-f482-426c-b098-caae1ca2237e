import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Checkbox } from '@mono/ui/checkbox';
import { VerificationCodeButton } from './verificationCodeButton';
import { userAuth } from '@/api/user';
import { useMutation } from '@tanstack/react-query';
import { useUserStore } from '@/store/user';
import { ReloadIcon } from '@radix-ui/react-icons';
import { UserLogin, UserLoginReq } from '@/types/user';
import { cn, zodIsPhone } from '@/lib/utils';
import { UserServiceAgreement } from './userServiceAgreement';
import { PrivacyPolicy } from './privacyPolicy';

const formSchema = z
  .object({
    phone: zodIsPhone,
    code: z.string(),
    remember: z.boolean().refine((value) => value, {
      message: '请勾选同意协议',
    }),
  })
  .refine((value) => !!value.code.length && value.code.length === 6, {
    message: '验证码格式不正确',
    path: ['code'],
  });

export function LoginContainer({
  loginNext,
}: {
  loginNext: (userLogin: UserLogin) => void;
}) {
  const setUser = useUserStore((state) => state.setUser);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
      code: '',
      remember: false,
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: userAuth,
    onSuccess: (data) => {
      console.log(data);
      setUser(data);
      loginNext(data);
    },
    onError: (error) => {
      // 登录失败
      console.log(error);
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
    const params: UserLoginReq = {
      phone: values?.phone,
      code: values?.code,
    };
    mutation.mutate(params);
  }

  return (
    <Card className={cn('w-[440px] h-max pt-[54px]')}>
      <CardHeader className="flex justify-center flex-row">
        <CardTitle className="text-2xl">蚁小二 ｜ 开放平台</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center p-10 bg-white rounded-b-xl">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="w-full flex justify-between flex-col text-base"
          >
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem className="mb-5">
                  <FormControl>
                    <Input
                      className="h-14 text-base"
                      type="tel"
                      placeholder="请输入11位手机号"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              key={'code'}
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem className="mb-5">
                  <FormControl>
                    <div className="relative">
                      <Input
                        maxLength={6}
                        className="h-14 pr-24 text-base"
                        placeholder="请输入验证码"
                        {...field}
                      />
                      <VerificationCodeButton
                        onVerifyCode={(code: string) => {
                          field.onChange(code);
                        }}
                      />
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
            <Button
              disabled={!form.formState.isValid || mutation.isPending}
              className="h-11 w-full mt-8 mb-2"
              type="submit"
            >
              {mutation.isPending && (
                <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
              )}
              登 录
            </Button>
            <div className="flex flex-row justify-between w-full h-8 mb-4">
              <FormField
                control={form.control}
                name="remember"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none text-muted-foreground">
                      <FormLabel>
                        我已阅读并接受《
                        <UserServiceAgreement />
                        》和《
                        <PrivacyPolicy />》
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
