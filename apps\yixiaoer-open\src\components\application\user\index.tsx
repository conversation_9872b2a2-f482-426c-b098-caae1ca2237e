import TableData from '@/components/table';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { memberList } from '@/api/user';
import { getTableColumns } from './columns';
import { useParams } from '@tanstack/react-router';

export function ApplicationUser() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const params = useParams({ strict: false });
  const applicationId = params.applicationId;
  // console.log(
  //   '%c ApplicationUser-applicationId:\n',
  //   'color:#FF7A45',
  //   applicationId
  // );

  const query = useQuery({
    queryKey: ['memberList', pagination, applicationId],
    queryFn: () =>
      memberList(applicationId as string, {
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
    enabled: !!applicationId,
  });

  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  return (
    <div className="w-full h-full flex flex-col px-[20px] py-5 overflow-hidden">
      <TableData
        columns={columns}
        data={query.data?.data || []}
        rowCount={query.data?.totalSize ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
