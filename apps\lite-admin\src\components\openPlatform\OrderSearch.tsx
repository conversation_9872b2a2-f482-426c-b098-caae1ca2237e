import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { DatePickerWithRange } from '@/components/DatePickerWithRange';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { OpenPlatformOrderParams, orderTypeMap } from '@/types/openPlatform';
import { Search, RotateCcw } from 'lucide-react';

interface OrderSearchProps {
  onSearch: (params: OpenPlatformOrderParams) => void;
  values: Partial<OpenPlatformOrderParams>;
}

type SearchForm = {
  orderNo?: string;
  appId?: string;
  orderType?: 'purchase' | 'gift' | '';
  dateRange?: {
    from: Date;
    to: Date;
  };
};

export function OrderSearch({ onSearch, values }: OrderSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      orderNo: values.orderNo || '',
      appId: values.appId || '',
      orderType: values.orderType || '',
      dateRange: values.startTime && values.endTime ? {
        from: new Date(values.startTime),
        to: new Date(values.endTime),
      } : undefined,
    },
  });

  const handleSubmit = (data: SearchForm) => {
    const params: OpenPlatformOrderParams = {
      orderNo: data.orderNo || undefined,
      appId: data.appId || undefined,
      orderType: data.orderType || undefined,
      startTime: data.dateRange?.from?.toISOString(),
      endTime: data.dateRange?.to?.toISOString(),
    };
    onSearch(params);
  };

  const handleReset = () => {
    form.reset({
      orderNo: '',
      appId: '',
      orderType: '',
      dateRange: undefined,
    });
    onSearch({});
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <FormField
            control={form.control}
            name="orderNo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>订单号</FormLabel>
                <Input
                  {...field}
                  placeholder="请输入订单号"
                />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>AppID</FormLabel>
                <Input
                  {...field}
                  placeholder="请输入AppID"
                />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="orderType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>订单类型</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部</SelectItem>
                    <SelectItem value="purchase">{orderTypeMap.purchase}</SelectItem>
                    <SelectItem value="gift">{orderTypeMap.gift}</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dateRange"
            render={({ field }) => (
              <FormItem>
                <FormLabel>下单时间</FormLabel>
                <DatePickerWithRange
                  onChange={(dateRange) => {
                    field.onChange(dateRange?.from && dateRange?.to ? {
                      from: dateRange.from,
                      to: dateRange.to,
                    } : undefined);
                  }}
                />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-2">
          <Button type="submit" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            搜索
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleReset}
            className="flex items-center gap-2"
          >
            <RotateCcw className="w-4 h-4" />
            重置
          </Button>
        </div>
      </form>
    </Form>
  );
}
