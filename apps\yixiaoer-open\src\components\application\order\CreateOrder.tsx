import { But<PERSON> } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import {
  createOrder,
  getInterest,
  giftOrder,
  openPlatformOrderPrice,
  renewOrder,
  upgradeOrder,
} from '@/api/order';
import { SelectTeam } from './SelectTeam';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { Textarea } from '@mono/ui/textarea';
import { formatPrice } from '@/lib/utils';
import { Team } from '@/types/team';
import { addDuration, formatYearMonthDay, isExpiredFun } from '@mono/utils/day';
import { cloneDeep } from 'lodash';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import { OrderType } from '@/types/order';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useParams } from '@tanstack/react-router';

export function Row({ label, value }: { label: string; value: ReactNode }) {
  return (
    <li className="flex items-center gap-6 text-sm">
      <div className="w-20 text-muted-foreground">{label}:</div>
      <span className={'line-clamp-1 flex-1'}>{value}</span>
    </li>
  );
}

// 表单验证 schema
const formSchema = z.object({
  interestCount: z.number().min(1, { message: '请输入权益包数量' }),
  interestId: z.string().min(1, { message: '请选择权益' }),
  month: z.string().optional(),
  payAmount: z.string(),
  teamId: z.string().min(1, { message: '请选择团队id' }),
  isPay: z.string().optional(),
  remark: z.string().optional(),
  giftDays: z.number().min(0),
  days: z.number().min(1),
});

// 时长: 自定义
const customDurationValue = 'customize';

interface CreateOrderProps {
  open: boolean;
  onClose: () => void;
}

export function CreateOrder({ open, onClose }: CreateOrderProps) {
  const [currentTeam, setCurrentTeam] = useState<Team | undefined>();
  const queryClient = useQueryClient();
  const [orderType, setOrderType] = useState<OrderType>('create');
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  const vipQuery = useQuery({
    queryFn: () =>
      getInterest().then((res) => {
        form.setValue('interestId', `${res.id}`);
        return res;
      }),
    queryKey: ['getInterest'],
    enabled: open,
  });

  const vipOften = useMemo(() => {
    return vipQuery.data?.vipOften || [];
  }, [vipQuery.data]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      teamId: '',
      interestCount: 1,
      payAmount: '',
      isPay: '',
      giftDays: 0,
      days: 1,
    },
  });

  const handleSuccess = () => {
    queryClient.refetchQueries({ queryKey: ['orderList'] });
    onClose();
    form.reset();
  };

  const mutation = useMutation({
    mutationFn: (data: Parameters<typeof createOrder>[0]) =>
      createOrder({ ...data, applicationId }),
    onSuccess: () => {
      handleSuccess();
      toast.success('订单创建成功');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '创建订单失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const reNewMutation = useMutation({
    mutationFn: (data: Parameters<typeof renewOrder>[0]) =>
      renewOrder({ ...data, applicationId }),
    onSuccess: () => {
      handleSuccess();
      toast.success('续费订单创建成功');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '续费订单创建失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const upgradeMutation = useMutation({
    mutationFn: (data: Parameters<typeof upgradeOrder>[0]) =>
      upgradeOrder({ ...data, applicationId }),
    onSuccess: () => {
      handleSuccess();
      toast.success('升级订单创建成功');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '升级订单创建失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const giftMutation = useMutation({
    mutationFn: (data: Parameters<typeof giftOrder>[0]) =>
      giftOrder({ ...data, applicationId }),
    onSuccess: () => {
      handleSuccess();
      toast.success('赠送订单创建成功');
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '赠送订单创建失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params = cloneDeep(values);
    if (!params.remark || params.remark === '') {
      delete params.remark;
    }
    const { remark, interestId, teamId, ...rest } = params;

    if (['renew', 'create'].includes(orderType) && !rest.month) {
      toast.error('请选择时长');
      return;
    }

    if (isCustomize) {
      rest.month = '0';
    } else {
      rest.days = 0;
    }

    const data = {
      ...rest,
      interestCount: Number(rest.interestCount),
      payAmount: Number(rest.payAmount),
      month: Number(rest.month),
      days: Number(rest.days),
      giftDays: Number(rest.giftDays),
    };

    switch (orderType) {
      case 'create':
        mutation.mutate({
          isPay: true,
          remark,
          interestId: interestId,
          interestCount: data.interestCount,
          teamId: teamId,
          month: data.month,
          payAmount: data.payAmount,
          days: data.days,
        });
        break;
      case 'renew':
        reNewMutation.mutate({
          isPay: true,
          interestId: interestId,
          month: data.month,
          teamId: teamId,
          payAmount: data.payAmount,
          remark: remark,
          days: data.days,
        });
        break;
      case 'upgrade':
        upgradeMutation.mutate({
          isPay: true,
          interestId: interestId,
          interestCount: data.interestCount,
          teamId: teamId,
          payAmount: data.payAmount,
          remark: remark,
        });
        break;
      case 'gift':
        giftMutation.mutate({
          giftDays: data.giftDays,
          interestId: interestId,
          teamId: teamId,
          remark: remark,
        });
        break;
    }
  }

  const [interestId, month, interestCount] = form.watch([
    'interestId',
    'month',
    'interestCount',
  ]);

  const isCustomize = month === customDurationValue;

  const currentOften = useMemo(() => {
    return vipOften.find((item) => `${item.mount}` === month);
  }, [month, vipOften]);

  const hasOften = orderType === 'upgrade' || !!currentOften || isCustomize;

  const priceQuery = useQuery({
    queryFn: () => {
      return openPlatformOrderPrice({
        interestCount: interestCount,
        interestId: interestId,
        month: isCustomize ? undefined : currentOften?.mount,
        orderType,
        teamId: currentTeam!.id,
        days: isCustomize ? form.getValues().days : undefined,
      });
    },
    queryKey: [
      'orderPrice',
      {
        interestCount,
        interestId,
        month: currentOften?.mount,
        orderType,
        teamId: currentTeam?.id,
        days: form.getValues().days,
      },
    ],
    enabled:
      !!interestId &&
      !!interestCount &&
      !!currentTeam &&
      hasOften &&
      !!form.getValues().days,
  });

  useEffect(() => {
    if (priceQuery?.data) {
      form.setValue(
        'payAmount',
        (
          priceQuery.data.orderAmount - priceQuery.data.discountAmount
        )?.toString()
      );
    }
  }, [priceQuery?.data]);

  useEffect(() => {
    if (!open) {
      form.reset();
      setOrderType('create');
      setCurrentTeam(undefined);
    }
  }, [form, open]);

  const setTeam = (team?: Team) => {
    setCurrentTeam(team);
    if (team) {
      setOrderType(team.isVip ? 'renew' : 'create');
    }
  };

  // 是否可以选数量
  const canSelectInterestCount = ['create', 'upgrade'].includes(orderType);

  const isSubmitting = [
    mutation.isPending,
    reNewMutation.isPending,
    upgradeMutation.isPending,
    giftMutation.isPending,
  ].some(Boolean);

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="!max-w-none w-[700px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>创建订单</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-4 m-4">
              <FormField
                control={form.control}
                name="teamId"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-24 flex-shrink-0">团队id</FormLabel>
                    <SelectTeam
                      value={field.value}
                      onChange={(value) => {
                        setTeam(value);
                        field.onChange(value ? value.id.toString() : '');
                      }}
                    />
                  </FormItem>
                )}
              />

              <div className="flex gap-1">
                <div className="font-medium w-24 flex-shrink-0">当前权益</div>
                {currentTeam && currentTeam.isVip ?
                  <ul className="flex flex-col gap-2 p-4 bg-background rounded-lg border">
                    <Row
                      label="到期时间"
                      value={formatYearMonthDay(
                        new Date(currentTeam.expiredAt)
                      )}
                    />
                  </ul>
                : <span>-</span>}
              </div>

              {orderType !== 'create' && (
                <div className="flex items-center gap-1 space-y-0">
                  <span className="w-24 flex-shrink-0 font-medium text-sm">
                    订单类型
                  </span>
                  <RadioGroup
                    defaultValue="renew"
                    value={orderType}
                    onValueChange={(value) => {
                      if (value === 'upgrade') {
                        form.setValue('month', '');
                      }
                      if (value === 'gift') {
                        form.setValue(
                          'month',
                          vipOften[0]?.mount.toString() || ''
                        );
                      }
                      setOrderType(value as OrderType);
                      form.setValue('days', 1);
                    }}
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="renew" id="r1" />
                      <Label htmlFor="r1">续费</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="upgrade" id="r2" />
                      <Label htmlFor="r2">升级</Label>
                    </div>
                  </RadioGroup>
                </div>
              )}

              {canSelectInterestCount && (
                <FormField
                  control={form.control}
                  name="interestCount"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-24 flex-shrink-0">
                        权益包数量
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={9999}
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}

              {orderType === 'gift' ?
                <FormField
                  control={form.control}
                  name="giftDays"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 space-y-0">
                      <FormLabel className="w-24 flex-shrink-0">
                        赠送天数
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="w-50"
                          min={1}
                          step={1}
                          type="number"
                          {...field}
                          onChange={(e) => {
                            const inputValue = Number(e.target.value);
                            if (/^[1-9]\d*$/.test(String(inputValue))) {
                              field.onChange(inputValue);
                            } else {
                              field.onChange(1);
                            }
                          }}
                        />
                      </FormControl>
                      <span className="text-sm text-muted-foreground ml-4">
                        赠送后到期日期：
                        {formatYearMonthDay(
                          addDuration(
                            field.value,
                            'day',
                            currentTeam?.expiredAt ?
                              new Date(currentTeam.expiredAt)
                            : new Date()
                          )
                        )}
                      </span>
                    </FormItem>
                  )}
                />
              : <>
                  {orderType !== 'upgrade' && (
                    <FormField
                      control={form.control}
                      name="month"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-1 space-y-0">
                          <FormLabel className="w-24 flex-shrink-0">
                            时长
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="请选择时长" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {vipOften.map((item) => (
                                <SelectItem
                                  key={item.mount}
                                  value={`${item.mount}`}
                                >{`${item.mount}个月${item.present ? `+送${item.present}个月` : ''}`}</SelectItem>
                              ))}
                              <SelectItem value={customDurationValue}>
                                自定义
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}

                  {isCustomize && (
                    <FormField
                      control={form.control}
                      name="days"
                      render={({ field }) => (
                        <FormItem className="flex items-center gap-1 space-y-0">
                          <FormLabel className="w-24 flex-shrink-0">
                            天数
                          </FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-1">
                              <Input
                                className="w-64"
                                min={1}
                                step={1}
                                type="number"
                                {...field}
                                onChange={(e) => {
                                  const inputValue = Number(e.target.value);
                                  if (/^[1-9]\d*$/.test(String(inputValue))) {
                                    field.onChange(inputValue);
                                  } else {
                                    field.onChange(1);
                                  }
                                }}
                              />
                              <span>天</span>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  )}

                  <div className="flex items-center gap-1 space-y-0">
                    <span className="w-24 flex-shrink-0 font-medium text-sm">
                      开通到期
                    </span>
                    {formatYearMonthDay(
                      addDuration(
                        isCustomize ?
                          form.watch('days')
                        : (currentOften?.mount ?? 0) +
                            (currentOften?.present ?? 0),
                        isCustomize ? 'day' : 'month',
                        (
                          isExpiredFun(
                            currentTeam?.expiredAt ?
                              new Date(currentTeam.expiredAt)
                            : undefined
                          )
                        ) ?
                          new Date()
                        : currentTeam?.expiredAt ?
                          new Date(currentTeam.expiredAt)
                        : new Date()
                      )
                    )}
                  </div>

                  {priceQuery.isLoading ?
                    <div className="h-16 w-full flex items-center justify-start">
                      <Loader2 className="w-5 h-5 animate-spin" />
                    </div>
                  : priceQuery.isSuccess && (
                      <>
                        <div className="flex items-center">
                          <p className="text-sm w-24 flex-shrink-0 font-medium">
                            订单金额
                          </p>
                          <p className="text-base font-bold flex items-center gap-2">
                            <span>
                              ¥{formatPrice(priceQuery.data.orderAmount)}
                            </span>
                          </p>
                        </div>
                        <div className="flex items-center">
                          <p className="text-sm w-24 flex-shrink-0 font-medium">
                            应付金额
                          </p>
                          <p className="text-base font-bold">
                            ¥
                            {formatPrice(
                              priceQuery.data.orderAmount -
                                priceQuery.data.discountAmount
                            )}
                          </p>
                        </div>
                      </>
                    )
                  }
                </>
              }

              <FormField
                control={form.control}
                name="remark"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-24 flex-shrink-0">
                      备注（选填）
                    </FormLabel>
                    <Textarea
                      rows={4}
                      placeholder="请输入"
                      className="resize-none"
                      {...field}
                    />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={handleClose}
                variant="ghost"
                type="button"
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                提交
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
