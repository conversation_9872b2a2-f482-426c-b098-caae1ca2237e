import { getRegisterScale } from '@/api/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { ChartConfig, ChartContainer } from '@mono/ui/chart';
import { Tabs, TabsList, TabsTrigger } from '@mono/ui/tabs';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import ReactECharts from 'echarts-for-react';

const chartConfig = {
  visitors: {
    label: 'Visitors'
  },
  androidRegisterNum: {
    label: '安卓'
  },
  iosRegisterNum: {
    label: 'ios'
  },
  macRegisterNum: {
    label: 'mac'
  },
  windowsRegisterNum: {
    label: 'windows'
  },
  otherRegisterNum: {
    label: '其他'
  },
} satisfies ChartConfig;

export function RegisterScale() {
  const [tab, setTab] = React.useState('all');
  const { data } = useQuery({
    queryKey: ['getRegisterScale', tab],
    queryFn: () => getRegisterScale(tab === 'thirty' ? 'thirty' : undefined),
  });

  const chartData = React.useMemo(() => {
    return Object.entries(data || {}).map(([browser, visitors]) => ({
      name: chartConfig[browser as keyof typeof chartConfig]?.label ?? browser,
      value: visitors
    }));
  }, [data]);

  const totalVisitors = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.value, 0);
  }, [chartData]);

  const option = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '注册占比',
          type: 'pie',
          radius: ['40%', '70%'],
          data: chartData,
        },
      ],
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `${totalVisitors.toLocaleString()}\n总注册数`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 12,
          },
        },
      ],
    }),
    [chartData, totalVisitors]
  );

  return (
    <Card className="w-full">
      <CardHeader className="flex items-center flex-row justify-between py-0 h-[76px]">
        <CardTitle className="text-xl">注册占比</CardTitle>
        <Tabs value={tab} onValueChange={setTab}>
          <TabsList>
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="thirty">近30日</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full">
          <ReactECharts
            option={option}
           className={'!w-full !h-full'}
          />
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
