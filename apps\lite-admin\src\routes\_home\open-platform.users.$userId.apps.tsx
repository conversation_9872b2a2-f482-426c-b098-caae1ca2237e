import { createFileRoute, useParams } from '@tanstack/react-router';
import { AppManagement } from '@/components/openPlatform';

function AppListPage() {
  const { userId } = useParams({
    from: '/_home/open-platform/users/$userId/apps',
  });
  return <AppManagement userId={userId} />;
}

export const Route = createFileRoute('/_home/open-platform/users/$userId/apps')(
  {
    component: AppListPage,
  }
);
