import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { ScrollArea } from '@mono/ui/scroll-area';
import { UserBase } from '@mono/ui/common/UserBase';
import { useQuery } from '@tanstack/react-query';
import { getTeamMembers } from '@/api/team';
import { useState } from 'react';
import { LoadingContainer } from '../loading';

export const TeamMembersDialog = ({
  children,
  id,
}: {
  children?: React.ReactNode;
  id: string;
}) => {
  const [open, setOpen] = useState(false);
  const query = useQuery({
    queryKey: ['getTeamMembers', id],
    queryFn: () => getTeamMembers(id),
    enabled: open,
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="!max-w-none w-[844px] p-0">
        <DialogTitle className="text-lg leading-6 font-medium pl-6 pt-[22px]">
          查看团队成员
        </DialogTitle>
        <VisuallyHidden>
          <DialogDescription />
        </VisuallyHidden>
        <ScrollArea
          style={{
            // maxHeight: 'calc(100vh - 80px)',
            maxHeight: '490px',
          }}
          className="overflow-hidden"
        >
          {query.isLoading && <LoadingContainer />}
          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
            {query.data?.map((item) => (
              <div
                className="px-4 py-[14px] bg-muted border border-solid border-muted rounded-lg"
                key={item.id}
              >
                <UserBase
                  size="xl"
                  id={item.id}
                  name={item.nickName}
                  avatar={item.avatar}
                  subtitle={item.phone}
                />
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
