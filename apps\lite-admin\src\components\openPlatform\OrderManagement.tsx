import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { DataTable } from '@/components/dataTable';
import { OrderSearch } from './OrderSearch';
import { OrderDetailDrawer } from './OrderDetailDrawer';
import { getOrderColumns } from './OrderColumns';
import { getExtendedOpenPlatformOrders } from '@/api/openPlatform';
import {
  ExtendedOpenPlatformOrder,
  ExtendedOpenPlatformOrderParams,
} from '@/types/openPlatform';
import { useSearch } from '@tanstack/react-router';

export function OrderManagement() {
  const search = useSearch({
    from: '/_home/open-platform/orders',
  }) as { appId?: string };
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [searchParams, setSearchParams] =
    useState<ExtendedOpenPlatformOrderParams>({
      // 如果从应用列表跳转过来，自动应用 appId 筛选
      appId: search?.appId || undefined,
    });
  const [selectedOrder, setSelectedOrder] =
    useState<ExtendedOpenPlatformOrder | null>(null);
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false);

  // 获取订单列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['extendedOpenPlatformOrders', pagination, searchParams],
    queryFn: () =>
      getExtendedOpenPlatformOrders({
        ...searchParams,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      }),
  });

  // 处理搜索
  const handleSearch = (params: ExtendedOpenPlatformOrderParams) => {
    setSearchParams({
      ...params,
      // 保持从应用列表跳转过来的 appId 筛选
      appId: params.appId || search?.appId || undefined,
    });
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  };

  // 查看订单详情
  const handleViewDetail = (order: ExtendedOpenPlatformOrder) => {
    setSelectedOrder(order);
    setDetailDrawerOpen(true);
  };

  // 关闭详情抽屉
  const handleCloseDetailDrawer = () => {
    setDetailDrawerOpen(false);
    setSelectedOrder(null);
  };

  const columns = getOrderColumns({
    onViewDetail: handleViewDetail,
  });

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">加载失败</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : '未知错误'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-full gap-4 overflow-hidden">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">订单管理</h1>
          <p className="text-muted-foreground">管理开放平台应用订单信息</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>搜索筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <OrderSearch onSearch={handleSearch} values={searchParams} />
        </CardContent>
      </Card>

      <Card className="overflow-auto">
        <CardHeader>
          <CardTitle>订单订单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={isLoading ? undefined : data?.data || []}
            rowCount={data?.total || 0}
            pagination={pagination}
            setPagination={setPagination}
          />
        </CardContent>
      </Card>

      <OrderDetailDrawer
        open={detailDrawerOpen}
        onClose={handleCloseDetailDrawer}
        orderDetail={selectedOrder || null}
      />
    </div>
  );
}
