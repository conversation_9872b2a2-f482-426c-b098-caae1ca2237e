import { useState } from 'react';
import { Label } from '@mono/ui/label';
import { Input } from '@mono/ui/input';
import { LoadingButton } from '../loadingButton';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { sendCoupon } from '@/api/coupon';

export function SendCouponForm({
  itemId,
  onClose,
}: {
  itemId: number;
  onClose: () => void;
}) {
  const [phone, setPhone] = useState('');
  const queryClient = useQueryClient();
  const sendMutation = useMutation({
    mutationFn: sendCoupon,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['getCoupons'] });
      onClose();
    },
  });

  const handleSend = () => {
    sendMutation.mutate({
      couponsId: itemId,
      phone,
    });
  };

  return (
    <div className="flex flex-col pt-2 gap-3">
      <div className="flex w-full flex-col gap-2">
        <Label htmlFor="phone">手机号</Label>
        <Input
          type="phone"
          id="phone"
          placeholder="请输入手机号"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
        />
      </div>
      <div className="flex justify-end">
        <LoadingButton
          isPending={sendMutation.isPending}
          disabled={sendMutation.isPending}
          onClick={handleSend}
        >
          发放
        </LoadingButton>
      </div>
    </div>
  );
}
