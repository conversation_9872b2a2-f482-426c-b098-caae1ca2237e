import { TeamPage } from '@/components/team';
import { TeamsReq } from '@/types/team';
import { createFileRoute } from '@tanstack/react-router';

type Search = Pick<
  TeamsReq,
  'phone' | 'expiredStartTime' | 'expiredEndTime' | 'salesType' | 'customerId'
> & {
  teamCode?: string;
};
export const Route = createFileRoute('/_home/team')({
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      phone: search?.phone as string,
      teamCode: search?.teamCode as string,
      salesType: search?.salesType as number,
      expiredStartTime: search?.expiredStartTime as number,
      expiredEndTime: search?.expiredEndTime as number,
      customerId: search?.customerId as string,
    };
  },
  component: TeamPage,
});
