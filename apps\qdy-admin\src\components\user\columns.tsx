import { createColumnHelper } from '@tanstack/react-table';
import { User } from '@/types/user';
import { formatDate } from '@/lib/utils/day';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { Button } from '@mono/ui/button';
import DropdownMenuComponent from '../DropdownMenuComponent.tsx';
import { DropdownMenuItem } from '@mono/ui/dropdown-menu';

const columnHelper = createColumnHelper<User & { action: string }>();

export const getUserColumns = (
  setChannel?: (user: User) => void,
  onDeleteUser?: (id: number) => void
) => {
  return [
    columnHelper.accessor('name', {
      cell: (info) => (
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarImage src={info.row.original.avatar} />
            <AvatarFallback>{info.getValue().substring(0, 2)}</AvatarFallback>
          </Avatar>
          <span>{info.getValue()}</span>
        </div>
      ),
      header: () => <span>用户</span>,
    }),
    columnHelper.accessor('phone', {
      cell: (info) => info.getValue(),
      header: () => <span>手机号</span>,
    }),
    columnHelper.accessor('createTime', {
      header: () => '注册时间',
      cell: (info) => (
        <span className="text-muted-foreground">
          {formatDate(info.getValue())}
        </span>
      ),
    }),
    columnHelper.accessor('channel.name', {
      cell: (info) =>
        info.getValue() ?? (
          <Button
            variant="link"
            className="w-max p-0 text-foreground"
            onClick={() => {
              if (setChannel) {
                setChannel(info.row.original);
              }
            }}
          >
            设置
          </Button>
        ),
      header: () => <span>渠道</span>,
    }),
    columnHelper.accessor('action', {
      header: () => <span>操作</span>,
      cell: (info) => (
        <DropdownMenuComponent className="!w-max !h-max">
          <DropdownMenuItem
            onClick={() => {
              // console.log('注销', info.row.original);
              onDeleteUser?.(info.row.original.id);
            }}
          >
            注销
          </DropdownMenuItem>
        </DropdownMenuComponent>
      ),
    }),
  ];
};
