import { But<PERSON> } from '@mono/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import md from '@/assets/privacyPolicy.md?raw';
import { ScrollArea } from '@mono/ui/scroll-area';

export function PrivacyPolicy() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          type="button"
          className="p-0 h-auto text-muted-foreground"
          variant="link"
        >
          隐私政策
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-7xl h-5/6 flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle>蚁小二隐私政策</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-full flex-1">
          <div className="prose max-w-none prosse-sm prose-indigo">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                a: ({ ...props }) => (
                  <a target="_blank" rel="noopener noreferrer" {...props} />
                ),
              }}
            >
              {md}
            </ReactMarkdown>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
