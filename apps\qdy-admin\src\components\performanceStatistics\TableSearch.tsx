import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem } from '@mono/ui/form';
import { omitBy } from 'lodash';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { reduceDuration } from '@mono/utils/day';
import { PerformanceSearchParams } from '@/types/performance';

type SearchForm = {
  dateRange?: {
    from: Date;
    to: Date;
  };
} & Omit<PerformanceSearchParams, 'startTime' | 'endTime'>;

export function TableSearch({
  onSearch,
}: {
  values?: PerformanceSearchParams;
  onSearch: (values: PerformanceSearchParams) => void;
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      dateRange: undefined,
    },
  });

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: PerformanceSearchParams = {
      startTime: values?.dateRange?.from?.getTime(),
      endTime: values.dateRange?.to?.getTime(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form {...form}>
      <form className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="dateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              {/* <FormLabel className="">选择时间段</FormLabel> */}
              <CalendarRange
                value={field.value}
                onChange={(value: { from?: Date; to?: Date } | undefined) => {
                  field.onChange(value);
                  if (
                    (value?.from && value?.to) ||
                    (!value?.from && !value?.to)
                  ) {
                    onSubmit(form.getValues());
                  }
                }}
                disabled={(date) => date < new Date(reduceDuration(2, 'M'))}
              />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
