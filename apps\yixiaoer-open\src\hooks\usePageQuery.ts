import { useState } from 'react';
import { PaginationState } from '@tanstack/react-table';
import { QueryKey, useQuery } from '@tanstack/react-query';
import { PageData } from '@/types';

export function usePageQuery<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TParams = Record<string, unknown>,
>(
  queryKey: QueryKey,
  queryFn: (params: { size: number; page: number }) => Promise<PageData<TData>>,
  params?: TParams
) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const query = useQuery({
    queryKey: [...queryKey, pagination, params],
    queryFn: () =>
      queryFn({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
        ...params,
      }),
  });
  return { query, pagination, setPagination };
}
