/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root';
import { Route as LoginImport } from './routes/login';
import { Route as AuthImport } from './routes/_auth';
import { Route as IndexImport } from './routes/index';
import { Route as AuthWechatStatisticsImport } from './routes/_auth/wechat-statistics';
import { Route as AuthUserImport } from './routes/_auth/user';
import { Route as AuthTeamImport } from './routes/_auth/team';
import { Route as AuthPerformanceStatisticsImport } from './routes/_auth/performance-statistics';
import { Route as AuthOverviewImport } from './routes/_auth/overview';
import { Route as AuthOrderImport } from './routes/_auth/order';
import { Route as AuthNoticeImport } from './routes/_auth/notice';
import { Route as AuthIpStatisticsImport } from './routes/_auth/ip-statistics';
import { Route as AuthCouponImport } from './routes/_auth/coupon';
import { Route as AuthChannelImport } from './routes/_auth/channel';
import { Route as AuthAppImport } from './routes/_auth/app';
import { Route as AuthAdminImport } from './routes/_auth/admin';
import { Route as AuthActivationRecordImport } from './routes/_auth/activation-record';
import { Route as AuthAccountImport } from './routes/_auth/account';
import { Route as AuthOrderIndexImport } from './routes/_auth/order/index';
import { Route as AuthOrderOrderNOImport } from './routes/_auth/order/$orderNO';

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any);

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any);

const AuthWechatStatisticsRoute = AuthWechatStatisticsImport.update({
  id: '/wechat-statistics',
  path: '/wechat-statistics',
  getParentRoute: () => AuthRoute,
} as any);

const AuthUserRoute = AuthUserImport.update({
  id: '/user',
  path: '/user',
  getParentRoute: () => AuthRoute,
} as any);

const AuthTeamRoute = AuthTeamImport.update({
  id: '/team',
  path: '/team',
  getParentRoute: () => AuthRoute,
} as any);

const AuthPerformanceStatisticsRoute = AuthPerformanceStatisticsImport.update({
  id: '/performance-statistics',
  path: '/performance-statistics',
  getParentRoute: () => AuthRoute,
} as any);

const AuthOverviewRoute = AuthOverviewImport.update({
  id: '/overview',
  path: '/overview',
  getParentRoute: () => AuthRoute,
} as any);

const AuthOrderRoute = AuthOrderImport.update({
  id: '/order',
  path: '/order',
  getParentRoute: () => AuthRoute,
} as any);

const AuthNoticeRoute = AuthNoticeImport.update({
  id: '/notice',
  path: '/notice',
  getParentRoute: () => AuthRoute,
} as any);

const AuthIpStatisticsRoute = AuthIpStatisticsImport.update({
  id: '/ip-statistics',
  path: '/ip-statistics',
  getParentRoute: () => AuthRoute,
} as any);

const AuthCouponRoute = AuthCouponImport.update({
  id: '/coupon',
  path: '/coupon',
  getParentRoute: () => AuthRoute,
} as any);

const AuthChannelRoute = AuthChannelImport.update({
  id: '/channel',
  path: '/channel',
  getParentRoute: () => AuthRoute,
} as any);

const AuthAppRoute = AuthAppImport.update({
  id: '/app',
  path: '/app',
  getParentRoute: () => AuthRoute,
} as any);

const AuthAdminRoute = AuthAdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthRoute,
} as any);

const AuthActivationRecordRoute = AuthActivationRecordImport.update({
  id: '/activation-record',
  path: '/activation-record',
  getParentRoute: () => AuthRoute,
} as any);

const AuthAccountRoute = AuthAccountImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => AuthRoute,
} as any);

const AuthOrderIndexRoute = AuthOrderIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthOrderRoute,
} as any);

const AuthOrderOrderNORoute = AuthOrderOrderNOImport.update({
  id: '/$orderNO',
  path: '/$orderNO',
  getParentRoute: () => AuthOrderRoute,
} as any);

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth': {
      id: '/_auth';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof AuthImport;
      parentRoute: typeof rootRoute;
    };
    '/login': {
      id: '/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof LoginImport;
      parentRoute: typeof rootRoute;
    };
    '/_auth/account': {
      id: '/_auth/account';
      path: '/account';
      fullPath: '/account';
      preLoaderRoute: typeof AuthAccountImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/activation-record': {
      id: '/_auth/activation-record';
      path: '/activation-record';
      fullPath: '/activation-record';
      preLoaderRoute: typeof AuthActivationRecordImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/admin': {
      id: '/_auth/admin';
      path: '/admin';
      fullPath: '/admin';
      preLoaderRoute: typeof AuthAdminImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/app': {
      id: '/_auth/app';
      path: '/app';
      fullPath: '/app';
      preLoaderRoute: typeof AuthAppImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/channel': {
      id: '/_auth/channel';
      path: '/channel';
      fullPath: '/channel';
      preLoaderRoute: typeof AuthChannelImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/coupon': {
      id: '/_auth/coupon';
      path: '/coupon';
      fullPath: '/coupon';
      preLoaderRoute: typeof AuthCouponImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/ip-statistics': {
      id: '/_auth/ip-statistics';
      path: '/ip-statistics';
      fullPath: '/ip-statistics';
      preLoaderRoute: typeof AuthIpStatisticsImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/notice': {
      id: '/_auth/notice';
      path: '/notice';
      fullPath: '/notice';
      preLoaderRoute: typeof AuthNoticeImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/order': {
      id: '/_auth/order';
      path: '/order';
      fullPath: '/order';
      preLoaderRoute: typeof AuthOrderImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/overview': {
      id: '/_auth/overview';
      path: '/overview';
      fullPath: '/overview';
      preLoaderRoute: typeof AuthOverviewImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/performance-statistics': {
      id: '/_auth/performance-statistics';
      path: '/performance-statistics';
      fullPath: '/performance-statistics';
      preLoaderRoute: typeof AuthPerformanceStatisticsImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/team': {
      id: '/_auth/team';
      path: '/team';
      fullPath: '/team';
      preLoaderRoute: typeof AuthTeamImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/user': {
      id: '/_auth/user';
      path: '/user';
      fullPath: '/user';
      preLoaderRoute: typeof AuthUserImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/wechat-statistics': {
      id: '/_auth/wechat-statistics';
      path: '/wechat-statistics';
      fullPath: '/wechat-statistics';
      preLoaderRoute: typeof AuthWechatStatisticsImport;
      parentRoute: typeof AuthImport;
    };
    '/_auth/order/$orderNO': {
      id: '/_auth/order/$orderNO';
      path: '/$orderNO';
      fullPath: '/order/$orderNO';
      preLoaderRoute: typeof AuthOrderOrderNOImport;
      parentRoute: typeof AuthOrderImport;
    };
    '/_auth/order/': {
      id: '/_auth/order/';
      path: '/';
      fullPath: '/order/';
      preLoaderRoute: typeof AuthOrderIndexImport;
      parentRoute: typeof AuthOrderImport;
    };
  }
}

// Create and export the route tree

interface AuthOrderRouteChildren {
  AuthOrderOrderNORoute: typeof AuthOrderOrderNORoute;
  AuthOrderIndexRoute: typeof AuthOrderIndexRoute;
}

const AuthOrderRouteChildren: AuthOrderRouteChildren = {
  AuthOrderOrderNORoute: AuthOrderOrderNORoute,
  AuthOrderIndexRoute: AuthOrderIndexRoute,
};

const AuthOrderRouteWithChildren = AuthOrderRoute._addFileChildren(
  AuthOrderRouteChildren
);

interface AuthRouteChildren {
  AuthAccountRoute: typeof AuthAccountRoute;
  AuthActivationRecordRoute: typeof AuthActivationRecordRoute;
  AuthAdminRoute: typeof AuthAdminRoute;
  AuthAppRoute: typeof AuthAppRoute;
  AuthChannelRoute: typeof AuthChannelRoute;
  AuthCouponRoute: typeof AuthCouponRoute;
  AuthIpStatisticsRoute: typeof AuthIpStatisticsRoute;
  AuthNoticeRoute: typeof AuthNoticeRoute;
  AuthOrderRoute: typeof AuthOrderRouteWithChildren;
  AuthOverviewRoute: typeof AuthOverviewRoute;
  AuthPerformanceStatisticsRoute: typeof AuthPerformanceStatisticsRoute;
  AuthTeamRoute: typeof AuthTeamRoute;
  AuthUserRoute: typeof AuthUserRoute;
  AuthWechatStatisticsRoute: typeof AuthWechatStatisticsRoute;
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthAccountRoute: AuthAccountRoute,
  AuthActivationRecordRoute: AuthActivationRecordRoute,
  AuthAdminRoute: AuthAdminRoute,
  AuthAppRoute: AuthAppRoute,
  AuthChannelRoute: AuthChannelRoute,
  AuthCouponRoute: AuthCouponRoute,
  AuthIpStatisticsRoute: AuthIpStatisticsRoute,
  AuthNoticeRoute: AuthNoticeRoute,
  AuthOrderRoute: AuthOrderRouteWithChildren,
  AuthOverviewRoute: AuthOverviewRoute,
  AuthPerformanceStatisticsRoute: AuthPerformanceStatisticsRoute,
  AuthTeamRoute: AuthTeamRoute,
  AuthUserRoute: AuthUserRoute,
  AuthWechatStatisticsRoute: AuthWechatStatisticsRoute,
};

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren);

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/account': typeof AuthAccountRoute;
  '/activation-record': typeof AuthActivationRecordRoute;
  '/admin': typeof AuthAdminRoute;
  '/app': typeof AuthAppRoute;
  '/channel': typeof AuthChannelRoute;
  '/coupon': typeof AuthCouponRoute;
  '/ip-statistics': typeof AuthIpStatisticsRoute;
  '/notice': typeof AuthNoticeRoute;
  '/order': typeof AuthOrderRouteWithChildren;
  '/overview': typeof AuthOverviewRoute;
  '/performance-statistics': typeof AuthPerformanceStatisticsRoute;
  '/team': typeof AuthTeamRoute;
  '/user': typeof AuthUserRoute;
  '/wechat-statistics': typeof AuthWechatStatisticsRoute;
  '/order/$orderNO': typeof AuthOrderOrderNORoute;
  '/order/': typeof AuthOrderIndexRoute;
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/account': typeof AuthAccountRoute;
  '/activation-record': typeof AuthActivationRecordRoute;
  '/admin': typeof AuthAdminRoute;
  '/app': typeof AuthAppRoute;
  '/channel': typeof AuthChannelRoute;
  '/coupon': typeof AuthCouponRoute;
  '/ip-statistics': typeof AuthIpStatisticsRoute;
  '/notice': typeof AuthNoticeRoute;
  '/overview': typeof AuthOverviewRoute;
  '/performance-statistics': typeof AuthPerformanceStatisticsRoute;
  '/team': typeof AuthTeamRoute;
  '/user': typeof AuthUserRoute;
  '/wechat-statistics': typeof AuthWechatStatisticsRoute;
  '/order/$orderNO': typeof AuthOrderOrderNORoute;
  '/order': typeof AuthOrderIndexRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/': typeof IndexRoute;
  '/_auth': typeof AuthRouteWithChildren;
  '/login': typeof LoginRoute;
  '/_auth/account': typeof AuthAccountRoute;
  '/_auth/activation-record': typeof AuthActivationRecordRoute;
  '/_auth/admin': typeof AuthAdminRoute;
  '/_auth/app': typeof AuthAppRoute;
  '/_auth/channel': typeof AuthChannelRoute;
  '/_auth/coupon': typeof AuthCouponRoute;
  '/_auth/ip-statistics': typeof AuthIpStatisticsRoute;
  '/_auth/notice': typeof AuthNoticeRoute;
  '/_auth/order': typeof AuthOrderRouteWithChildren;
  '/_auth/overview': typeof AuthOverviewRoute;
  '/_auth/performance-statistics': typeof AuthPerformanceStatisticsRoute;
  '/_auth/team': typeof AuthTeamRoute;
  '/_auth/user': typeof AuthUserRoute;
  '/_auth/wechat-statistics': typeof AuthWechatStatisticsRoute;
  '/_auth/order/$orderNO': typeof AuthOrderOrderNORoute;
  '/_auth/order/': typeof AuthOrderIndexRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/account'
    | '/activation-record'
    | '/admin'
    | '/app'
    | '/channel'
    | '/coupon'
    | '/ip-statistics'
    | '/notice'
    | '/order'
    | '/overview'
    | '/performance-statistics'
    | '/team'
    | '/user'
    | '/wechat-statistics'
    | '/order/$orderNO'
    | '/order/';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | ''
    | '/login'
    | '/account'
    | '/activation-record'
    | '/admin'
    | '/app'
    | '/channel'
    | '/coupon'
    | '/ip-statistics'
    | '/notice'
    | '/overview'
    | '/performance-statistics'
    | '/team'
    | '/user'
    | '/wechat-statistics'
    | '/order/$orderNO'
    | '/order';
  id:
    | '__root__'
    | '/'
    | '/_auth'
    | '/login'
    | '/_auth/account'
    | '/_auth/activation-record'
    | '/_auth/admin'
    | '/_auth/app'
    | '/_auth/channel'
    | '/_auth/coupon'
    | '/_auth/ip-statistics'
    | '/_auth/notice'
    | '/_auth/order'
    | '/_auth/overview'
    | '/_auth/performance-statistics'
    | '/_auth/team'
    | '/_auth/user'
    | '/_auth/wechat-statistics'
    | '/_auth/order/$orderNO'
    | '/_auth/order/';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  AuthRoute: typeof AuthRouteWithChildren;
  LoginRoute: typeof LoginRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_auth",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/account",
        "/_auth/activation-record",
        "/_auth/admin",
        "/_auth/app",
        "/_auth/channel",
        "/_auth/coupon",
        "/_auth/ip-statistics",
        "/_auth/notice",
        "/_auth/order",
        "/_auth/overview",
        "/_auth/performance-statistics",
        "/_auth/team",
        "/_auth/user",
        "/_auth/wechat-statistics"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_auth/account": {
      "filePath": "_auth/account.tsx",
      "parent": "/_auth"
    },
    "/_auth/activation-record": {
      "filePath": "_auth/activation-record.tsx",
      "parent": "/_auth"
    },
    "/_auth/admin": {
      "filePath": "_auth/admin.tsx",
      "parent": "/_auth"
    },
    "/_auth/app": {
      "filePath": "_auth/app.tsx",
      "parent": "/_auth"
    },
    "/_auth/channel": {
      "filePath": "_auth/channel.tsx",
      "parent": "/_auth"
    },
    "/_auth/coupon": {
      "filePath": "_auth/coupon.tsx",
      "parent": "/_auth"
    },
    "/_auth/ip-statistics": {
      "filePath": "_auth/ip-statistics.tsx",
      "parent": "/_auth"
    },
    "/_auth/notice": {
      "filePath": "_auth/notice.tsx",
      "parent": "/_auth"
    },
    "/_auth/order": {
      "filePath": "_auth/order.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/order/$orderNO",
        "/_auth/order/"
      ]
    },
    "/_auth/overview": {
      "filePath": "_auth/overview.tsx",
      "parent": "/_auth"
    },
    "/_auth/performance-statistics": {
      "filePath": "_auth/performance-statistics.tsx",
      "parent": "/_auth"
    },
    "/_auth/team": {
      "filePath": "_auth/team.tsx",
      "parent": "/_auth"
    },
    "/_auth/user": {
      "filePath": "_auth/user.tsx",
      "parent": "/_auth"
    },
    "/_auth/wechat-statistics": {
      "filePath": "_auth/wechat-statistics.tsx",
      "parent": "/_auth"
    },
    "/_auth/order/$orderNO": {
      "filePath": "_auth/order/$orderNO.tsx",
      "parent": "/_auth/order"
    },
    "/_auth/order/": {
      "filePath": "_auth/order/index.tsx",
      "parent": "/_auth/order"
    }
  }
}
ROUTE_MANIFEST_END */
