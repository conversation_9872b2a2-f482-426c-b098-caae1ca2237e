// 封装 axios
import { logout } from '@/lib/utils/auth';
import { useUserStore } from '@/store/user';
import axios from 'axios';
import { AxiosResponse, AxiosRequestConfig } from 'axios';
import { toast } from 'sonner';

interface ApiResponse<T> {
  data: T; // 实际数据
  message: string; // 状态消息
  statusCode: number; // 状态码
}

export const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 50000,
});

instance.interceptors.request.use((config) => {
  const authorization = useUserStore.getState().user?.authorization;
  if (authorization) {
    config.headers.authorization = authorization;
  }
  return config;
});

instance.interceptors.response.use(
  (response) => {
    const apiResponse = response.data;
    if (apiResponse.statusCode !== 0) {
      toast.error(apiResponse.message);
      return Promise.reject(apiResponse.message);
    } else {
      return response;
    }
  },
  (error) => {
    console.log(error);
    // 未登录
    if (error?.response?.status === 401) {
      // toast.error("请先登录");
      return logout();
    }
    toast.error(error?.response?.data?.message || error.message);
    return Promise.reject(error);
  }
);

// 通用请求函数
async function makeRequest<T>(config: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<ApiResponse<T>> =
    await instance.request(config);
  return response.data.data; // 返回 ApiResponse 数据
}

export { makeRequest };
