import { Input } from './ui/input';
import { Button } from './ui/button';
import { Plus } from 'lucide-react';
import { useRef, useState } from 'react';
import { Separator } from './ui/separator';
import { PhotoView, PhotoProvider } from 'react-photo-view';
import { toast } from 'sonner';
import { cn } from '@mono/utils';

function SelectFile({
  url,
  accept = 'image/*',
  maxSize = 5 * 1024 * 1024,
  onchange,
  className,
}: {
  url?: string;
  accept?: string;
  maxSize?: number;
  onchange?: (file: File, url: string, setUrl: (url: string) => void) => void;
  className?: string;
}) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  // 显示的 url
  const [showUrl, setShowUrl] = useState(url);

  // 当按钮被点击时，触发文件选择对话框
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  // 当文件被选择时，触发事件并调用回调函数
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > maxSize) {
        toast.error('文件大小不能超过 5MB');
        return;
      }
      const url = URL.createObjectURL(file);
      setShowUrl(url);
      if (onchange) {
        onchange(file, url, setShowUrl);
      }
    }
  };

  return (
    <div
      className={cn('w-28 h-28 relative overflow-hidden rounded', className)}
    >
      <Button
        type="button"
        onClick={handleButtonClick}
        variant="outline"
        size="icon"
        className="w-full h-full"
      >
        <Plus className="h-8 w-8" strokeWidth={1} />
      </Button>
      {showUrl && (
        <div className="w-full h-full absolute top-0 left-0">
          <img src={showUrl} className="w-full h-full object-cover" />
          <div className="absolute bottom-2 right-5 flex items-center justify-center">
            <PhotoProvider bannerVisible={false}>
              <PhotoView src={showUrl}>
                <Button
                  type="button"
                  variant="default"
                  size="sm"
                  className="h-6 w-10 rounded-l-full rounded-r-none bg-primary/70"
                >
                  查看
                </Button>
              </PhotoView>
            </PhotoProvider>
            <div className="w-[1px] flex items-center h-6 bg-primary/70">
              <Separator
                orientation="vertical"
                className="h-2 bg-[#FFFFFF4D]"
              />
            </div>
            <Button
              type="button"
              onClick={handleButtonClick}
              size="sm"
              className="h-6 w-10 rounded-l-none rounded-r-full bg-primary/70"
            >
              替换
            </Button>
          </div>
        </div>
      )}

      <Input
        ref={fileInputRef}
        className="hidden"
        id="picture"
        accept={accept}
        type="file"
        onChange={handleFileChange}
      />
    </div>
  );
}

export { SelectFile };
