import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { OrderParams } from '@/types/order';
import { CreateOrder } from '@/components/order/CreateOrder.tsx';
import { SelectChannel } from '../user/SelectChannel';
import { orderStatusMap, orderTypeMap, saleTypeMap } from '@/config/order';
import { getDayEndTime } from '@mono/utils/day';
import { useEffect } from 'react';

export const numberRegex = /^[1-9]\d*$/;

const formSchema = z.object({
  orderNo: z.string().optional(),
  phone: z.string().optional(),
  invitationCode: z.string().optional(),
  channelId: z.string().optional(),
  orderType: z.string().optional(),
  salesType: z.string().optional(),
  // vip到期时间段
  vipDateRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  payDateRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  // status
  orderStatus: z.enum(['all', 'pending', 'canceled', 'success']),
  payAmount: z
    .string()
    .refine(
      (value) => {
        if (value) {
          return numberRegex.test(value);
        } else {
          return true;
        }
      },
      {
        message: '格式不正确',
      }
    )
    .optional(),
});

export function TableSearch({
  values,
  onSearch: onSearch,
}: {
  values: OrderParams;
  onSearch: (values: OrderParams) => void;
}) {
  const { setValue, ...form } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...values,
      payAmount: '',
      orderStatus:
        values.orderStatus ?
          (values.orderStatus as 'all' | 'pending' | 'canceled' | 'success')
        : 'all',
      orderType: 'all',
      salesType: values.salesType ?? 'Buy',
    },
  });

  useEffect(() => {
    if (values?.orderStartTime && values?.orderEndTime) {
      setValue('vipDateRange', {
        from: new Date(values.orderStartTime),
        to: new Date(values.orderEndTime),
      });
    }
  }, [values]);

  const isBlank = (value: unknown) => {
    return value === '' || value === null || value === undefined;
  };

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    const vipDateFrom = values.vipDateRange?.from?.getTime();
    let vipDateTo = values.vipDateRange?.to?.getTime();
    if (
      !isBlank(vipDateFrom) &&
      !isBlank(vipDateTo) &&
      vipDateTo === vipDateFrom
    ) {
      // 获取当天的最后时间
      vipDateTo = getDayEndTime(vipDateTo);
    }
    const params: OrderParams = {
      orderNo: values.orderNo,
      phone: values.phone,
      invitationCode: values.invitationCode?.trim(),
      payStartTime: values.payDateRange?.from?.getTime(),
      payEndTime: values.payDateRange?.to?.getTime(),
      orderStartTime: vipDateFrom,
      orderEndTime: vipDateTo,
      orderStatus:
        values.orderStatus === 'all' ? undefined : values.orderStatus,
      orderType: values.orderType === 'all' ? undefined : values.orderType,
      salesType: values.salesType === 'all' ? undefined : values.salesType,
      channelId: values.channelId?.trim(),
      payAmount: values.payAmount ? parseInt(values.payAmount) : undefined,
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form setValue={setValue} {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 p-0.5"
      >
        <CreateOrder />
        <FormField
          control={form.control}
          name="orderNo"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入订单号搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="输入手机号搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="invitationCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队ID</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索团队ID" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="orderStatus"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>订单状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单来源" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {Object.keys(orderStatusMap).map((key) => {
                    return (
                      <SelectItem key={key} value={key}>
                        {orderStatusMap[key as keyof typeof orderStatusMap]}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="orderType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>订单类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {Object.keys(orderTypeMap)
                    ?.filter((x) => x !== 'gift')
                    .map((key) => {
                      return (
                        <SelectItem key={key} value={key}>
                          {orderTypeMap[key as keyof typeof orderTypeMap]}
                        </SelectItem>
                      );
                    })}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="salesType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>销售类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择销售类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {Object.keys(saleTypeMap).map((key) => {
                    return (
                      <SelectItem key={key} value={key}>
                        {saleTypeMap[key as keyof typeof saleTypeMap]}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="vipDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">创建时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="payDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>支付时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="channelId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道</FormLabel>
              <FormControl>
                <SelectChannel
                  value={`${field.value}`}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        {
          <FormField
            control={form.control}
            name="payAmount"
            render={({ field }) => (
              <FormItem className="flex items-center gap-1 space-y-0">
                <FormLabel>付款金额大于</FormLabel>
                <FormControl>
                  <Input
                    className="w-40"
                    placeholder="输入付款金额"
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        }
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
