import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  SheetTitle,
} from '@mono/ui/sheet';
import { Badge } from '@mono/ui/badge';
import {
  RechargeOrder,
  rechargeOrderStatusMap,
  rechargeOrderStatusStyleMap,
  rechargeTypeMap,
} from '@/types/openPlatform';
import { format } from 'date-fns';

interface RechargeDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  order: RechargeOrder | null;
}

export function RechargeDetailDrawer({
  open,
  onClose,
  order,
}: RechargeDetailDrawerProps) {
  if (!order) return null;

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>充值订单详情</SheetTitle>
          <SheetDescription>查看充值订单的详细信息</SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">基本信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  充值订单号
                </label>
                <div className="mt-1 font-mono text-sm">
                  {order.rechargeOrderNo}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  手机号码
                </label>
                <div className="mt-1 font-medium">{order.phone}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  订单类型
                </label>
                <div className="mt-1">
                  <Badge variant="outline">
                    {rechargeTypeMap[order.rechargeType]}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  订单状态
                </label>
                <div className="mt-1">
                  <Badge
                    className={
                      rechargeOrderStatusStyleMap[
                        order.rechargeStatus as keyof typeof rechargeOrderStatusStyleMap
                      ]
                    }
                  >
                    {
                      rechargeOrderStatusMap[
                        order.rechargeStatus as keyof typeof rechargeOrderStatusMap
                      ]
                    }
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* 金额信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">金额信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  购买数量
                </label>
                <div className="mt-1 font-medium text-orange-600">
                  {order.rechargeAmount.toLocaleString()} 蚁币
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  付款金额
                </label>
                <div className="mt-1 font-medium">¥{order.paymentAmount}</div>
              </div>
            </div>
          </div>

          {/* 应用信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">应用信息</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  应用名称
                </label>
                <div className="mt-1">{order.applicationName}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  应用ID
                </label>
                <div className="mt-1 font-mono text-sm">
                  {order.applicationId}
                </div>
              </div>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">时间信息</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  创建时间
                </label>
                <div className="mt-1">
                  {format(new Date(order.createdAt), 'yyyy-MM-dd HH:mm:ss')}
                </div>
              </div>
              {order.rechargeTime && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    完成时间
                  </label>
                  <div className="mt-1">
                    {format(
                      new Date(order.rechargeTime),
                      'yyyy-MM-dd HH:mm:ss'
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 备注信息 */}
          {order.remark && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">备注信息</h3>
              <div className="p-3 bg-muted rounded-md">
                <div className="text-sm">{order.remark}</div>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
