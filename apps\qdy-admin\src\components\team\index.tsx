import { teamList } from '@/api/team';
import TableData from '@/components/table';
import { UserTableSearch } from '@/components/team/TableSearch';
// import { VipDetail } from "@/components/user/VipDetail";
import { getTableColumns } from '@/components/team/columns';
import { OverView } from '@/components/team/Overview';
import { TeamParams } from '@/types/team';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useEffect, useMemo, useRef, useState } from 'react';
import { FinancialRecords } from '@/components/team/FinancialRecords';
import { useSearch } from '@tanstack/react-router';

export function TeamRoute() {
  const {
    tName,
    invitationCode,
    from,
    phone,
    salesType,
    expiredEndTime,
    expiredStartTime,
  } = useSearch({ strict: false });
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 30,
  });
  const [open, setOpen] = useState(false);
  const [openFinancialRecords, setOpenFinancialRecords] = useState(false);
  const teamId = useRef(0);
  const [searchParams, setSearchParams] = useState<TeamParams>({
    name: tName,
    invitationCode: invitationCode,
    phone: phone,
    salesType: salesType !== undefined ? salesType : undefined,
    vipExpirationStartTime: expiredStartTime ?? undefined,
    vipExpirationEndTime: expiredEndTime ?? undefined,
  });
  const [sortByPlatformAccounts, setSortByPlatformAccounts] = useState<
    string | null
  >(null);
  const [sortByMembers, setSortByMembers] = useState<string | null>(null);

  const columns = useMemo(() => {
    return getTableColumns(
      (id) => {
        viewDetailData(id);
      },
      sortByPlatformAccounts,
      sortByMembers,
      (sort: string) => {
        setSortByPlatformAccounts(sort);
        setSortByMembers(null);

        setPagination({ pageIndex: 0, pageSize: 30 });
        setSearchParams({
          ...searchParams,
          sort: sort,
        } as TeamParams);
      },
      (sort: string) => {
        setSortByPlatformAccounts(null);
        setSortByMembers(sort);

        setPagination({ pageIndex: 0, pageSize: 30 });
        setSearchParams({
          ...searchParams,
          sort: sort,
        } as TeamParams);
      }
    );
  }, [sortByMembers, sortByPlatformAccounts]);

  const query = useQuery({
    queryKey: ['teamList', pagination, searchParams],
    queryFn: () =>
      teamList({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
        ...searchParams,
      }),
  });

  function viewDetailData(id: number) {
    teamId.current = id;
    setOpen(true);
  }

  useEffect(() => {
    if (invitationCode && tName && from === 'OverViewPage') {
      setSearchParams({
        name: tName,
        invitationCode: invitationCode,
      } as TeamParams);
    }
    if ((tName || phone) && from === 'OrderPage') {
      setSearchParams({ name: tName, phone: phone } as TeamParams);
    }
  }, [from, invitationCode, phone, tName]);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <UserTableSearch
        values={searchParams}
        onSearch={(values) => {
          setPagination({ pageIndex: 0, pageSize: 30 });
          setSearchParams(values);
        }}
      />
      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
      <OverView teamId={teamId.current} open={open} setOpen={setOpen} />

      {openFinancialRecords && (
        <FinancialRecords
          open={openFinancialRecords}
          setOpen={setOpenFinancialRecords}
          id={teamId.current}
        />
      )}
    </div>
  );
}
