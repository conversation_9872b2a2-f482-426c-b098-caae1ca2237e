{"name": "yixiaoer-open", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:pre": "vite build --mode staging", "build:prod": "vite build --mode production", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "lint-staged": "lint-staged"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@mono/ui": "workspace:*", "@mono/utils": "workspace:*", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-visually-hidden": "^1.1.0", "@tanstack/react-query": "^5.40.1", "@tanstack/react-router": "^1.35.3", "@tanstack/react-table": "^8.17.3", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "immer": "^10.1.1", "lodash": "^4.17.21", "lucide-react": "^0.390.0", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-markdown": "10.1.0", "recharts": "^2.12.7", "remark-gfm": "4.0.1", "socket.io-client": "4.7.5", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@mono/config-tailwind": "workspace:*", "@mono/config-ts": "workspace:*", "@tanstack/react-query-devtools": "^5.40.1", "@tanstack/router-devtools": "^1.35.3", "@tanstack/router-vite-plugin": "^1.34.8", "@types/lodash": "^4.17.5", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0"}}