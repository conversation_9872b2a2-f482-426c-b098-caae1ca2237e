import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { Admin, AdminDTO } from '@/types/admin';

// 获取管理员列表
// GET /users
// 接口ID：219353550
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219353550
export const userList = (params: PageQuery) => {
  return makeRequest<PageData<Admin>>({
    url: '/users',
    method: 'GET',
    params,
  });
};

// 创建管理员
// POST /users
// 接口ID：219353551
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219353551
export const createUser = (params: AdminDTO) => {
  return makeRequest({
    url: '/users',
    method: 'POST',
    data: params,
  });
};

// 修改管理员信息
// PUT /users/{usersId}
// 接口ID：219353553
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219353553
export const updateUser = (params: AdminDTO) => {
  return makeRequest({
    url: `/users/${params.userId}`,
    method: 'PUT',
    data: params,
  });
};

// 删除管理员
// DELETE /users
// 接口ID：219353552
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-219353552
export const deleteUser = (usersId: string) => {
  return makeRequest({
    url: `/users/${usersId}`,
    method: 'DELETE',
  });
};
