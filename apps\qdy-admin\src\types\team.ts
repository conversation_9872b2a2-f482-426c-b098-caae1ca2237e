import { SaleType } from './order';

export type Team = {
  createTime: number;
  expirationTime: number;
  isVip: number;
  name: string;
  platformAccountNumberLimit: number;
  id: number;
  teamMemberNumberLimit: number;
  vip: TeamVip;
  avatar: string;
  members: number;
  platformAccountCount: number;
  platformAccounts: Account[];
  invitationCode: string;
  hasRefund: boolean;
  Refund?: RefundRecord[];
  owner: {
    avatar: string;
    id: number;
    name: string;
    phone: string;
    state: number;
  };
  salesType: SaleType;
  orders?: number;
};
export type Account = {
  accessToken: string;
  accountRole: string;
  avatar: string;
  createTime: string;
  expiresIn: number;
  id: number;
  name: string;
  openId: string;
  refreshExpiresIn: number;
  refreshTime: string;
  refreshToken: string;
  teamId: number;
  platform: number;
};

// 退费记录
export type RefundRecord = {
  id: number;
  actualRefundAmount: number;
  refundAmount: number;
  createTime: string;
  refundNo: string;
  teamId: number;
  remark: string;
  realityPrice: number;
  orderInfo: OrderRefundItem[];
};

export interface OrderRefundItem {
  orderNo: string;
  actualRefundAmount: number;
  refundAmount: number;
}
// commentDosageLimit: 69;
// createTime: *************;
// expirationTime: ************;
// fansGroupManageLimit: 0;
// groupDosageLimit: 51;
// id: 1;
// platformAccountNumberLimit: 49;
// signDosageLimit: 82;
// teamId: 1;
// teamMemberNumberLimit: 14;
// uploadImageLimit: 0;
export type TeamVip = {
  commentDosageLimit: number;
  createTime: number;
  expirationTime: number;
  fansGroupManageLimit: number;
  groupDosageLimit: number;
  id: number;
  platformAccountNumberLimit: number;
  signDosageLimit: number;
  teamId: number;
  teamMemberNumberLimit: number;
  uploadImageLimit: number;
};

export type TeamParams = {
  name?: string;
  phone?: string;
  createStartTime?: number;
  createEndTime?: number;
  invitationCode?: string;
  isVip?: boolean;
  vipExpirationStartTime?: number;
  vipExpirationEndTime?: number;
  salesType?: string;
  sort?: string;
};

/**
 * VipCreateDTO
 */
export type VipCreateDTO = {
  commentDosageLimit: number;
  expirationTime: number;
  fansGroupManageLimit?: number;
  groupDosageLimit: number;
  platformAccountNumberLimit?: number;
  price: number;
  remark?: string;
  signDosageLimit: number;
  teamId: number;
  teamMemberNumberLimit: number;
  uploadImageLimit: number;
};

export interface TeamBase {
  avatar: string;
  createTime: number;
  id: number;
  invitationCode: string;
  name: string;
  ownerId: number;
}

export interface OverviewResponseTypes {
  autoCommentCount: number;
  autoGroupCount: number;
  autoSingleCount: number;
  commentCount: number;
  commentPeopleCount: number;
  createTime: string;
  groupCount: number;
  manageGroupCount: number;
  singleCount: number;
  singlePeopleCount: number;
  teamId: number;
  uploadImageCount: number;
  wechatMessageCount: number;
  wechatAutoMessageCount: number;
  wechatCommentCount: number;
  wechatAutoCommentCount: number;
  wechatCommentPeopleCount: number;
  wechatSinglePeopleCount: number;
}

export interface TeamOrderStatus {
  avatar: string;
  hasPendingOrder: boolean;
  hasVip: boolean;
  id: number;
  name: string;
  balance: number;
  createTime: string;
  payTime: string;
  expirationTime: string;
  freeMonth: number;
  interestCount: number;
  month: number;
  price: number;
  remainingDay: number;
  refundPrice: {
    price: number;
  };
  day: number;
}

// 资金变动记录
export type FinancialRecord = {
  amount: number;
  type: string;
  remark: string;
};
