import { io, Socket } from 'socket.io-client';
import { handlerMsgQueue } from './handlerMsg';
import { ServiceMessage } from '@/types/service-message';

enum SocketStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
}

export class SocketClient {
  private socket: Socket;
  public status: SocketStatus;

  constructor(token: string) {
    this.socket = io(import.meta.env.VITE_WSS_URL, {
      transports: ['websocket'],
      query: {
        authorization: token,
      },
      path: `${import.meta.env.VITE_WSS_PATH}/socket.io`,
    });
    this.status = SocketStatus.DISCONNECTED;

    this.socket.on('connect', () => {
      this.status = SocketStatus.CONNECTED;
      console.log('Socket connected');
    });

    this.socket.on('disconnect', (reason) => {
      this.status = SocketStatus.DISCONNECTED;
      console.log('Socket disconnected');
      console.log('Disconnected reason:\n', reason);
    });

    this.socket.on('error', () => {
      this.status = SocketStatus.ERROR;
      console.log('Socket error');
    });

    // 在这里处理接收到的消息
    this.socket.on('messages', (data: unknown) => {
      console.log('Received data:', data);
      handlerMsgQueue(data as ServiceMessage);
    });
  }

  public sendEvent(event: string, data: string): void {
    this.socket.emit(event, data);
  }

  // 断开连接
  public disconnect(): void {
    this.socket.disconnect();
  }
}

export let socket: SocketClient | null = null;

// init
export const openSocket = (token: string) => {
  socket = new SocketClient(token);
};

// 断开连接
export const closeSocket = () => {
  if (socket) {
    socket.disconnect();
  }
};
