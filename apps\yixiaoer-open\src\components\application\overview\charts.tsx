import { AreaChart, Area, XAxis, CartesianGrid } from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';

const chartConfig = {
  newAccountPoints: {
    label: '开通账号',
    color: 'hsl(var(--chart-1))',
  },
  usedAccountPoints: {
    label: '使用账号',
    color: 'hsl(var(--chart-2))',
  },
  newTraffic: {
    label: '开通流量',
    color: 'hsl(var(--chart-3))',
  },
  usedTraffic: {
    label: '使用流量',
    color: 'hsl(var(--chart-4))',
  },
} satisfies ChartConfig;

export type ChartItem = {
  date: string;
  newTraffic: number;
  usedTraffic: number;
  newAccountPoints?: number;
  usedAccountPoints?: number;
};

export default function Charts({
  list,
}: {
  list: ChartItem[];
}) {
  return (
    <ChartContainer
      config={chartConfig}
      className="min-h-[200px] h-full w-full"
    >
      <AreaChart data={list} accessibilityLayer>
        <XAxis dataKey="date" />
        <CartesianGrid vertical={false} />
        {/* {list.length > 0 && <Tooltip />} */}
        <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
        <ChartLegend content={<ChartLegendContent />} />
        <defs>
          <linearGradient id="colorNewAccountPoints" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="hsl(var(--chart-1))"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="hsl(var(--chart-1))"
              stopOpacity={0.1}
            />
          </linearGradient>
          <linearGradient id="colorUsedAccountPoints" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="hsl(var(--chart-2))"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="hsl(var(--chart-2))"
              stopOpacity={0.1}
            />
          </linearGradient>
          <linearGradient id="colorNewTraffic" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="hsl(var(--chart-3))"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="hsl(var(--chart-3))"
              stopOpacity={0.1}
            />
          </linearGradient>
          <linearGradient id="colorUsedTraffic" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="hsl(var(--chart-4))"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="hsl(var(--chart-4))"
              stopOpacity={0.1}
            />
          </linearGradient>
        </defs>

        <Area
          type="monotone"
          dataKey="newAccountPoints"
          stroke="hsl(var(--chart-1))"
          fill="url(#colorNewAccountPoints)"
          stackId="1"
        />
        <Area
          type="monotone"
          dataKey="usedAccountPoints"
          stroke="hsl(var(--chart-2))"
          fill="url(#colorUsedAccountPoints)"
          stackId="1"
        />
        <Area
          type="monotone"
          dataKey="newTraffic"
          stroke="hsl(var(--chart-3))"
          fill="url(#colorNewTraffic)"
          stackId="2"
        />
        <Area
          type="monotone"
          dataKey="usedTraffic"
          stroke="hsl(var(--chart-4))"
          fill="url(#colorUsedTraffic)"
          stackId="2"
        />
      </AreaChart>
    </ChartContainer>
  );
}
