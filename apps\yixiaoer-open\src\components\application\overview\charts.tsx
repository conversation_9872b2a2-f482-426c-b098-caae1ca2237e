import { AreaChart, Area, XAxis, CartesianGrid } from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';

const chartConfig = {
  openAccount: {
    label: '开通账号',
    color: 'hsl(var(--chart-1))',
  },
  useAccount: {
    label: '使用账号',
    color: 'hsl(var(--chart-2))',
  },
  openTraffic: {
    label: '开通流量',
    color: 'hsl(var(--chart-3))',
  },
  useTraffic: {
    label: '使用流量',
    color: 'hsl(var(--chart-4))',
  },
} satisfies ChartConfig;

export type ChartItem = {
  name: string;
  comment: number;
  single: number;
  group?: number;
};

export default function Charts({
  list,
  hiddenGroup = false,
  hiddenComment = false,
}: {
  list: ChartItem[];
  hiddenGroup?: boolean;
  hiddenComment?: boolean;
}) {
  return (
    <ChartContainer
      config={chartConfig}
      className="min-h-[200px] h-full w-full"
    >
      <AreaChart data={list} accessibilityLayer>
        <XAxis dataKey="name" />
        <CartesianGrid vertical={false} />
        {/* {list.length > 0 && <Tooltip />} */}
        <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
        <ChartLegend content={<ChartLegendContent />} />
        <defs>
          <linearGradient id="colorSingle" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor="var(--color-single)"
              stopOpacity={0.8}
            />
            <stop
              offset="95%"
              stopColor="var(--color-single)"
              stopOpacity={0.1}
            />
          </linearGradient>
          {!hiddenGroup && (
            <linearGradient id="colorGroup" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-group)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--color-group)"
                stopOpacity={0.1}
              />
            </linearGradient>
          )}
          {!hiddenComment && (
            <linearGradient id="colorComment" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-comment)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--color-comment)"
                stopOpacity={0.1}
              />
            </linearGradient>
          )}
        </defs>

        <Area
          type="monotone"
          dataKey="single"
          stroke="var(--color-single)"
          fill="url(#colorSingle)"
        />
        {!hiddenGroup && (
          <Area
            type="monotone"
            dataKey="group"
            stroke="var(--color-group)"
            fill="url(#colorGroup)"
          />
        )}
        {!hiddenComment && (
          <Area
            type="monotone"
            dataKey="comment"
            stroke="var(--color-comment)"
            fill="url(#colorComment)"
          />
        )}
      </AreaChart>
    </ChartContainer>
  );
}
