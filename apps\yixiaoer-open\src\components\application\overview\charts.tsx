import { LineChart, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';

import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';

const chartConfig = {
  newAccountPoints: {
    label: '开通账号',
    color: 'hsl(var(--chart-1))',
  },
  usedAccountPoints: {
    label: '使用账号',
    color: 'hsl(var(--chart-2))',
  },
  newTraffic: {
    label: '开通流量',
    color: 'hsl(var(--chart-3))',
  },
  usedTraffic: {
    label: '使用流量',
    color: 'hsl(var(--chart-4))',
  },
} satisfies ChartConfig;

export type ChartItem = {
  date: string;
  newTraffic: number;
  usedTraffic: number;
  newAccountPoints?: number;
  usedAccountPoints?: number;
};

export default function Charts({ list }: { list: ChartItem[] }) {
  // 格式化日期显示（只显示月-日）
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}-${date.getDate()}`;
  };

  // 处理数据，格式化日期
  const formattedData = list.map((item) => ({
    ...item,
    displayDate: formatDate(item.date),
  }));

  return (
    <ChartContainer
      config={chartConfig}
      className="min-h-[400px] h-full w-full"
    >
      <LineChart
        data={formattedData}
        margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="displayDate"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: '#d0d0d0' }}
          axisLine={{ stroke: '#d0d0d0' }}
        />
        <YAxis
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: '#d0d0d0' }}
          axisLine={{ stroke: '#d0d0d0' }}
        />
        <ChartTooltip
          content={
            <ChartTooltipContent
              labelFormatter={(value) => `日期: ${value}`}
              formatter={(value, name) => [
                `${value}`,
                chartConfig[name as keyof typeof chartConfig]?.label || name,
              ]}
            />
          }
        />
        <ChartLegend content={<ChartLegendContent />} />

        <Line
          type="monotone"
          dataKey="newAccountPoints"
          stroke="hsl(var(--chart-1))"
          strokeWidth={2}
          dot={{ fill: 'hsl(var(--chart-1))', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: 'hsl(var(--chart-1))', strokeWidth: 2 }}
        />
        <Line
          type="monotone"
          dataKey="usedAccountPoints"
          stroke="hsl(var(--chart-2))"
          strokeWidth={2}
          dot={{ fill: 'hsl(var(--chart-2))', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: 'hsl(var(--chart-2))', strokeWidth: 2 }}
        />
        <Line
          type="monotone"
          dataKey="newTraffic"
          stroke="hsl(var(--chart-3))"
          strokeWidth={2}
          dot={{ fill: 'hsl(var(--chart-3))', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: 'hsl(var(--chart-3))', strokeWidth: 2 }}
        />
        <Line
          type="monotone"
          dataKey="usedTraffic"
          stroke="hsl(var(--chart-4))"
          strokeWidth={2}
          dot={{ fill: 'hsl(var(--chart-4))', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: 'hsl(var(--chart-4))', strokeWidth: 2 }}
        />
      </LineChart>
    </ChartContainer>
  );
}
