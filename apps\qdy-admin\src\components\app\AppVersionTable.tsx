import TableData from '@/components/table';
import { usePageQuery } from '@/hooks/usePageQuery';
import { ColumnDef } from '@tanstack/react-table';
import { AppVersion, AppVersionParams } from '@/types/app';
import { getAppVersions } from '@/api/app';

export function AppVersionTable({
  params,
  columns,
}: {
  columns: ColumnDef<AppVersion, unknown>[];
  params?: AppVersionParams;
}) {
  const { query, pagination, setPagination } = usePageQuery(
    ['getAppVersions'],
    getAppVersions,
    params
  );

  return (
    <TableData
      columns={columns}
      data={query.data?.data}
      rowCount={query.data?.total ?? 0}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}
