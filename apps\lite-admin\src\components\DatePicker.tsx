'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import { Calendar } from '@mono/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { zhCN } from 'date-fns/locale';
import { useEffect } from 'react';

interface DatePickerProps {
  value?: Date | undefined;
  onChange: (date: Date | undefined) => void;
  className?: string;
}

export function DatePicker({ value, onChange, className }: DatePickerProps) {
  const [date, setDate] = React.useState<Date>();
  const [open, setOpen] = React.useState(false);

  const popoverChange = () => {
    setOpen(!open);
    if (open) {
      onChange(date);
    }
  };

  useEffect(() => {
    if (value) {
      setDate(value);
    }
  }, [value]);

  return (
    <Popover open={open} onOpenChange={popoverChange} modal>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            `min-w-[280px] justify-start text-left font-normal ${className}`,
            !date && 'text-muted-foreground'
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ?
            format(date, 'y-MM-dd', { locale: zhCN })
          : <span>请选择日期</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          locale={zhCN}
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
