import { createFileRoute, Outlet, useMatches } from '@tanstack/react-router';
import { UserManagement } from '@/components/openPlatform';

export const Route = createFileRoute('/_home/open-platform/users')({
  component: () => {
    const matches = useMatches();
    // 检查是否有子路由匹配
    const hasChildRoute = matches.some(match =>
      match.routeId.includes('/open-platform/users/') &&
      match.routeId.includes('/apps')
    );

    return hasChildRoute ? <Outlet /> : <UserManagement />;
  },
});
