'use client';

import * as React from 'react';
import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';

import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@mono/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
// import { debounce } from 'lodash';
import { getApplications } from '@/api/openPlatform';
import { Loader2 } from 'lucide-react';
import { debounce } from 'lodash';

export function SelectApplication({
  value,
  onChange,
}: {
  value: string;
  onChange: (value?: string) => void;
}) {
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState('');

  const applicationsQuery = useQuery({
    queryFn: () => getApplications('', searchText),
    queryKey: ['getApplications', searchText],
    enabled: !!searchText,
  });

  // 优化搜索
  const searchFun = useMemo(
    () => debounce(setSearchText, 300),
    [setSearchText]
  );

  const currentApplication = useMemo(() => {
    if (value) {
      return applicationsQuery?.data?.applications.find(
        (application) => application.id === value
      );
    }
    return undefined;
  }, [applicationsQuery.data, value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[180px] justify-between whitespace-normal"
        >
          {currentApplication ?
            <>{currentApplication.name}</>
          : <>选择应用</>}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[180px]">
        <Command shouldFilter={false}>
          <CommandInput
            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
              searchFun(e.target.value);
            }}
            placeholder="请输入名称搜索"
            className="h-9"
          />
          <CommandList>
            {applicationsQuery.isLoading ?
              <div className="py-6 flex justify-center">
                <Loader2 className="w-5 h-5 animate-spin" />
              </div>
            : <CommandEmpty>未找到应用</CommandEmpty>}
            <CommandGroup>
              {applicationsQuery.data &&
                applicationsQuery.data.applications?.map((application) => (
                  <CommandItem
                    key={application.id}
                    value={application.id}
                    className="w-full flex items-center gap-2 justify-between"
                    onSelect={(currentValue) => {
                      onChange(
                        currentValue === value ? undefined : application.id
                      );
                      setOpen(false);
                    }}
                  >
                    <span>{application.name}</span>
                    <CheckIcon
                      className={cn(
                        'ml-auto h-4 w-4',
                        value === `${application.id}` ? 'opacity-100' : (
                          'opacity-0'
                        )
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
