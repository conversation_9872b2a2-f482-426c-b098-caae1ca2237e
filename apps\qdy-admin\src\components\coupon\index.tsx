import { Create } from '@/components/coupon/create.tsx';
import { TableBase } from '@/components/coupon/table.tsx';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import { getColumns } from '@/components/coupon/columns.tsx';
import { alertBaseManager } from '@/components/alertBase.tsx';
import { Coupon } from '@/types/coupon.ts';
import { ColumnDef } from '@tanstack/react-table';
import { deleteCoupon } from '@/api/coupon';
import { dialogBaseManager } from '../dialogBase';
import { SendCouponForm } from './sendCouponForm';

export function CouponPage() {
  const queryClient = useQueryClient();
  const [currentData, setCurrentData] = useState<Coupon>();
  const deleteMutation = useMutation({
    mutationFn: deleteCoupon,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['getCoupons'] });
    },
  });
  const columns = useMemo(() => {
    return getColumns((item, type) => {
      if (type === 'update') {
        setCurrentData({ ...item });
      } else if (type === 'del') {
        alertBaseManager.open({
          title: '删除优惠券',
          description: '确定删除该优惠券吗？',
          onSubmit: () => {
            deleteMutation.mutate(item.id.toString());
          },
        });
      } else if (type === 'send') {
        dialogBaseManager.open({
          title: '发放优惠券',
          render: (onClose) => (
            <SendCouponForm itemId={item.id} onClose={onClose} />
          ),
        });
      }
    });
  }, [deleteMutation, setCurrentData]);
  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <div>
        <Create coupon={currentData} />
      </div>
      <TableBase columns={columns as ColumnDef<Coupon, unknown>[]} />
    </div>
  );
}
