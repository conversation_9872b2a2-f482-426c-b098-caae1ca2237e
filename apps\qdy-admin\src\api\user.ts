// 用户注册/登录
// POST /users/auth
// 接口ID：167862365

import {
  User,
  UserInfo,
  UserLogin,
  UserLoginReq,
  UserParams,
  UserTwoFactorReq,
} from '@/types/user';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';

// 接口地址：https://app.apifox.com/project/4330611/apis/api-167862365
export const userAuth = (params: UserLoginReq) => {
  return makeRequest<UserLogin>({
    url: '/users/auth',
    method: 'POST',
    data: params,
  });
};

// 二次验证mfa
// POST /users/two/factor/auth
// 接口ID：256642204
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-256642204
export const userTwoFactorAuth = (params: UserTwoFactorReq) => {
  return makeRequest<UserLogin>({
    url: '/users/two/factor/auth',
    method: 'POST',
    data: params,
  });
};

// 获取用户信息
// GET /users/info
// 接口ID：167862057
// 接口地址：https://app.apifox.com/project/4330611/apis/api-167862057
export const userInfo = () => {
  return makeRequest<UserInfo>({
    url: '/users/info',
    method: 'GET',
  });
};

// 退出登录
// DELETE /users/auth
// 接口ID：183259867
// 接口地址：https://www.apifox.cn/project/4645177/apis/api-183259867

export const userLogout = () => {
  return makeRequest({
    url: '/users/auth',
    method: 'DELETE',
  });
};
// 更新用户信息
// PATCH /users/info
// 接口ID：167862058
// 接口地址：https://app.apifox.com/project/4330611/apis/api-167862058

export const userInfoUpdate = (params: Partial<UserInfo>) => {
  return makeRequest({
    url: '/users/info',
    method: 'PATCH',
    data: params,
  });
};

// 获取用户列表
export const getUsers = (params: PageQuery & UserParams) => {
  return makeRequest<PageData<User>>({
    url: '/members',
    method: 'GET',
    params,
  });
};

// 设置渠道
// PUT /members/{id}/channels
// 接口ID：228291212
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-228291212
export const setChannel = (id: number, channelCode: string) => {
  return makeRequest({
    url: `/members/${id}/channels`,
    method: 'PUT',
    data: {
      channelCode: channelCode,
    },
  });
};

// 删除用户（注销用户）
// DELETE /members/{id}
// 接口ID：230988889
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-230988889
export const deleteUser = (id: number, phone: string) => {
  return makeRequest({
    url: `/members/${id}`,
    method: 'DELETE',
    params: {
      phone,
    },
  });
};
