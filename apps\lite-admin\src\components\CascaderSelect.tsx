import {
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { useState } from 'react';
import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils.ts';

export interface CategoryModelBase<T> {
  id: string;
  text: string;
  children?: CategoryModelBase<T>[];
}

interface CascaderProps {
  data: CategoryModelBase<unknown>[];
  onChange: (value: CategoryModelBase<unknown>[]) => void;
}

export const CascaderSelect = ({ data = [], onChange }: CascaderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string[]>([]);
  const [childrenList, setChildrenList] = useState<
    CategoryModelBase<unknown>[]
  >([]);
  const [selectRow, setSelectRow] = useState<CategoryModelBase<unknown>>();

  const onParentChange = (parent: CategoryModelBase<unknown>) => {
    setSelectRow(parent);
    setSelectedText([parent.text]);
    setChildrenList(parent.children || []);
    if (parent.children?.length === 0) {
      onChange([parent]);
      setIsOpen(false);
    }
  };

  const onChildClick = (child: CategoryModelBase<unknown>) => {
    const updatedRow = { ...selectRow, children: [child] };
    setSelectRow(updatedRow as CategoryModelBase<unknown>);
    onChange([updatedRow] as CategoryModelBase<unknown>[]);
    setSelectedText([...selectedText, child.text]); // 设置最终选中的值
    setIsOpen(false); // 关闭下拉菜单
  };

  const handleMenuToggle = (open: boolean) => {
    // if (!open && selectedText.length === 1) {
    //   // 当关闭菜单且尚未选择任何内容时，保留原状态
    //   setSelectedText([]); // 清空选中项
    //   setChildrenList([]); // 清空子级展示
    //   setSelectRow(undefined); // 清空选中项
    // }
    setIsOpen(open); // 更新菜单状态
  };

  return (
    <div>
      <Select
        open={isOpen}
        onOpenChange={handleMenuToggle}
        defaultValue={selectedText.join('/')}
      >
        <SelectTrigger className={'w-[160px] h-9'}>
          <SelectValue
            placeholder={selectedText.join('/') || '请选择代理地区'}
          />
        </SelectTrigger>
        <SelectContent>
          <div className="relative flex z-[100]">
            {/* 第一层菜单：父级选项 */}

            <div
              className={cn(
                'w-[180px]  border-gray-200 max-h-[300px] overflow-y-auto'
              )}
            >
              {data.length === 0 && <div className={'px-2 my-4'}>暂无数据</div>}
              {data.map((item, index) => (
                <div
                  key={index}
                  className="px-2 py-2 cursor-pointer hover:bg-[#f1f5f9] rounded-md flex items-center justify-between"
                  onClick={() => onParentChange(item)}
                >
                  {item.text}
                  {item.children && item.children.length > 0 && (
                    <ChevronRight size={20} color="#9c9fa2" />
                  )}
                </div>
              ))}
            </div>

            {/* 第二层菜单：子级选项 */}
            {childrenList && childrenList.length > 0 && (
              <div className="w-[180px] max-h-[300px] overflow-y-auto">
                {childrenList?.map((child, index) => (
                  <div
                    key={index}
                    className="px-2 py-2 cursor-pointer hover:bg-[#f1f5f9] rounded-md"
                    onClick={() => onChildClick(child)} // 选择子级
                  >
                    {child.text}
                  </div>
                ))}
              </div>
            )}
          </div>
        </SelectContent>
      </Select>
    </div>
  );
};
