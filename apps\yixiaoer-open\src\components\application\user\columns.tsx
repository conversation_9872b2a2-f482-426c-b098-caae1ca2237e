import { ColumnDef } from '@tanstack/react-table';
import { Member } from '@/types/user';
import { formatDate } from '@/lib/utils/day';

export function getTableColumns(): Array<ColumnDef<Member>> {
  return [
    {
      header: '口令码',
      accessorKey: 'name',
      cell: ({ row }) => {
        const { id } = row.original;
        return <span className="text-muted-foreground">{id}</span>;
      },
    },
    {
      header: '账号昵称',
      accessorKey: 'username',
      cell: ({ row }) => {
        const { nickName } = row.original;
        return <span className="text-muted-foreground">{nickName}</span>;
      },
    },
    {
      header: '注册时间',
      accessorKey: '',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
  ];
}
