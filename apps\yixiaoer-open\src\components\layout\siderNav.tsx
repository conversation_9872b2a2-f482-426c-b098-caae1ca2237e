import { Link, useParams } from '@tanstack/react-router';
import { getNavs } from './navs';
// import { useUserStore } from '@/store/user';
import { ScrollArea } from '@mono/ui/scroll-area';
import { useApplicationDetail } from '@/hooks/useApplicationDetail';

export function SiderNav() {
  // const user = useUserStore((state) => state.user);
  // console.log('%c user: ', 'color:#FF7A45', user);
  const params = useParams({ strict: false });
  const applicationId = params.applicationId as string;
  const { data: applicationDetail } = useApplicationDetail();

  // 如果没有应用ID，不显示导航
  if (!applicationId) {
    return null;
  }

  const navs = getNavs(applicationId, applicationDetail?.applicationType);

  return (
    <aside className="flex flex-col h-full overflow-hidden w-[180px]">
      <ScrollArea className="flex-1 h-0">
        <nav className="flex flex-col gap-3 px-3 pt-[18px]">
          {navs.map((item) => (
            <Link
              key={item.path}
              activeProps={{
                className: 'bg-[#F1F1FD] text-[#4F46E5]',
              }}
              className="w-full flex items-center gap-2 px-3 py-2 transition-all rounded-lg text-sm font-medium text-[#222222] hover:text-[#4F46E5]"
              to={item.path}
            >
              {item.icon}
              <span>{item.name}</span>
            </Link>
          ))}
        </nav>
      </ScrollArea>
    </aside>
  );
}
