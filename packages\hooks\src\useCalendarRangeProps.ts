import { useCallback, useMemo } from 'react';
import { getDayEndTime, getTimestampByTime } from '@mono/utils/day';

export function useCalendarRangeProps<T extends { from?: number; to?: number }>(
  value: T,
  onChange: (value?: T) => void
) {
  const dateRange = useMemo(() => {
    return value.from ?
        {
          from: new Date(value.from!),
          to: value.to ? new Date(value.to!) : undefined,
        }
      : undefined;
  }, [value]);
  const onValueChange = useCallback(
    (value?: { from?: Date; to?: Date }) => {
      console.log(value);
      let from: number | undefined;
      let to: number | undefined;
      if (value && value.from) {
        from = getTimestampByTime(value.from);
        if (value.to) {
          to = getDayEndTime(value.to);
        }
      }
      onChange({ from, to } as T);
    },
    [onChange]
  );
  return [dateRange, onValueChange] as const;
}
