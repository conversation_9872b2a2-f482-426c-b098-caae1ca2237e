import { Button } from '@mono/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
import { ModeToggle } from './mode-toggle';
import { getUserInfo, logout } from '@/composables/use-authorize';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { useMutation } from '@tanstack/react-query';
import { userLoginOut } from '@/api/user';

export function Mine() {
  const mutation = useMutation({
    mutationFn: userLoginOut,
    mutationKey: ['userLoginOut'],
    onSuccess: (data: object) => {
      console.debug('%c data:\n', 'color:#FF7A45', data);
      logout();
    },
    onError: (error) => {
      // 登录失败
      console.debug(error);
    },
  });

  const handleLogout = () => {
    mutation.mutate();
  };

  return (
    <div className="flex items-center justify-between">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex justify-between w-32 gap-1 pl-0 pr-4 overflow-hidden rounded-full"
          >
            <Avatar className="w-[35px] h-[35px]">
              <AvatarImage src={getUserInfo()?.avatar} alt="@shadcn" />
              <AvatarFallback>
                {getUserInfo()?.username.toUpperCase().substring(0, 2)}
              </AvatarFallback>
            </Avatar>
            <span className="truncate">{getUserInfo()?.username}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>我的</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>退出登录</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ModeToggle />
    </div>
  );
}
