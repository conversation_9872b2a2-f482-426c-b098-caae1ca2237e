import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { Textarea } from '@mono/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { createRechargeOrder, getUserApps } from '@/api/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  phone: z.string().min(11, '请输入正确的手机号').max(11, '请输入正确的手机号'),
  appId: z.string().min(1, '请选择应用'),
  antCoins: z.number().min(1, '蚁币数量必须大于0'),
  orderType: z.enum(['purchase', 'gift'], {
    required_error: '请选择购买类型',
  }),
  amount: z.number().min(0, '付款金额不能小于0'),
  remark: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface CreateOrderModalProps {
  open: boolean;
  onClose: () => void;
  userId?: string;
}

export function CreateOrderModal({ open, onClose, userId }: CreateOrderModalProps) {
  const queryClient = useQueryClient();
  const [selectedUserId, setSelectedUserId] = useState<string>('');

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
      appId: '',
      antCoins: 0,
      orderType: 'purchase',
      amount: 0,
      remark: '',
    },
  });

  // 获取用户应用列表（当选择了用户时）
  const { data: userApps } = useQuery({
    queryKey: ['userApps', selectedUserId],
    queryFn: () => getUserApps({ userId: selectedUserId }),
    enabled: !!selectedUserId,
  });

  const mutation = useMutation({
    mutationFn: createRechargeOrder,
    onSuccess: () => {
      toast.success('充值订单创建成功');
      queryClient.invalidateQueries({ queryKey: ['extendedOpenPlatformOrders'] });
      form.reset();
      onClose();
    },
    onError: (error: any) => {
      toast.error(error?.message || '充值订单创建失败');
    },
  });

  const onSubmit = (data: FormData) => {
    mutation.mutate({
      ...data,
      userId: selectedUserId,
    });
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      setSelectedUserId('');
      onClose();
    }
  };

  // 当手机号变化时，可以根据手机号查找用户ID（这里简化处理）
  const handlePhoneChange = (phone: string) => {
    // TODO: 根据手机号查找用户ID的逻辑
    // 这里暂时使用手机号作为用户ID
    if (phone.length === 11) {
      setSelectedUserId(phone);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>创建充值订单</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>手机号 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入手机号"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handlePhoneChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="appId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>应用筛选 *</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择应用" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {userApps?.applications?.map((app) => (
                        <SelectItem key={app.appId} value={app.appId}>
                          {app.name} ({app.appId})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="antCoins"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>蚁币数量 *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="请输入蚁币数量"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="orderType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>购买类型 *</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={field.onChange}
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="purchase" id="purchase" />
                        <Label htmlFor="purchase">购买</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="gift" id="gift" />
                        <Label htmlFor="gift">赠送</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>付款金额</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="请输入付款金额"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入备注信息（可选）"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                创建订单
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
