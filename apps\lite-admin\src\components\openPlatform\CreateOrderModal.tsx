import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { Textarea } from '@mono/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { createRechargeOrder, getApplications } from '@/api/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  phone: z.string().min(11, '请输入正确的手机号').max(11, '请输入正确的手机号'),
  applicationType: z.enum(['TECHNICAL_SERVICE', 'CHANNEL_PARTNER'], {
    required_error: '请选择应用类型',
  }),
  applicationId: z.string().min(1, '请选择应用'),
  virtualCoinAmount: z.number().min(1, '蚁币数量必须大于0'),
  rechargeType: z.enum(['BANK_TRANSFER', 'GIFT'], {
    required_error: '请选择购买类型',
  }),
  paymentAmount: z.number().min(0, '付款金额不能小于0'),
  remark: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface CreateOrderModalProps {
  open: boolean;
  onClose: () => void;
  defaultAppId?: string; // 从应用列表跳转时的默认应用ID
}

export function CreateOrderModal({
  open,
  onClose,
  defaultAppId,
}: CreateOrderModalProps) {
  const queryClient = useQueryClient();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [applicationType, setApplicationType] = useState('');

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
      applicationType: undefined,
      applicationId: defaultAppId || '',
      virtualCoinAmount: 0,
      rechargeType: 'BANK_TRANSFER',
      paymentAmount: 0,
      remark: '',
    },
  });

  // 基于手机号和应用类型查询用户应用列表
  const {
    data: userApps,
    isLoading: isLoadingApps,
    error: appsError,
  } = useQuery({
    queryKey: ['userAppsByPhone', phoneNumber, applicationType],
    queryFn: () => getApplications(phoneNumber, applicationType),
    enabled: phoneNumber.length === 11 && !!applicationType, // 手机号为11位且选择了应用类型时才查询
    retry: false,
  });

  const mutation = useMutation({
    mutationFn: createRechargeOrder,
    onSuccess: () => {
      toast.success('充值订单创建成功');
      queryClient.invalidateQueries({ queryKey: ['rechargeOrders'] });
      form.reset();
      onClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '充值订单创建失败';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    mutation.mutate(data);
  };

  // 处理手机号变化
  const handlePhoneChange = (phone: string) => {
    setPhoneNumber(phone);
    // 如果手机号不是11位，清空应用选择
    if (phone.length !== 11) {
      form.setValue('applicationId', '');
    }
  };

  // 处理应用类型变化
  const handleApplicationTypeChange = (type: string) => {
    setApplicationType(type);
    // 应用类型改变时，清空应用选择
    form.setValue('appId', '');
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      setPhoneNumber('');
      setApplicationType('');
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>创建充值订单</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>手机号 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入手机号"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handlePhoneChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="applicationType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>应用类型 *</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleApplicationTypeChange(value);
                      }}
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="TECHNICAL_SERVICE" id="technical" />
                        <Label htmlFor="technical">技术服务商</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="CHANNEL_PARTNER" id="channel" />
                        <Label htmlFor="channel">渠道商</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="applicationId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>应用筛选 *</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            phoneNumber.length !== 11 ? '请先输入完整手机号'
                            : !applicationType ?
                              '请先选择应用类型'
                            : isLoadingApps ?
                              '加载应用中...'
                            : userApps?.applications?.length === 0 ?
                              '该用户暂无应用'
                            : '请选择应用'
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {userApps?.applications?.map((app) => (
                        <SelectItem key={app.id} value={app.id}>
                          {app.name} ({app.appId})
                        </SelectItem>
                      ))}
                      {userApps?.applications?.length === 0 &&
                        phoneNumber.length === 11 &&
                        !isLoadingApps && (
                          <div className="px-2 py-1 text-sm text-muted-foreground">
                            该用户暂无应用
                          </div>
                        )}
                    </SelectContent>
                  </Select>
                  {appsError && (
                    <div className="text-sm text-red-500 mt-1">
                      查询应用失败，请重试
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="virtualCoinAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>蚁币数量 *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="请输入蚁币数量"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rechargeType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>购买类型 *</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={field.onChange}
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem
                          value="BANK_TRANSFER"
                          id="BANK_TRANSFER"
                        />
                        <Label htmlFor="BANK_TRANSFER">购买</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="GIFT" id="GIFT" />
                        <Label htmlFor="GIFT">赠送</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>付款金额</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="请输入付款金额"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入备注信息（可选）"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                创建订单
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
