import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { Textarea } from '@mono/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { createRechargeOrder, getApplications } from '@/api/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  phone: z.string().min(11, '请输入正确的手机号').max(11, '请输入正确的手机号'),
  appId: z.string().min(1, '请选择应用'),
  antCoins: z.number().min(1, '蚁币数量必须大于0'),
  orderType: z.enum(['purchase', 'gift'], {
    required_error: '请选择购买类型',
  }),
  amount: z.number().min(0, '付款金额不能小于0'),
  remark: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface CreateOrderModalProps {
  open: boolean;
  onClose: () => void;
  defaultAppId?: string; // 从应用列表跳转时的默认应用ID
}

export function CreateOrderModal({ open, onClose, defaultAppId }: CreateOrderModalProps) {
  const queryClient = useQueryClient();
  const [phoneNumber, setPhoneNumber] = useState('');

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: '',
      appId: defaultAppId || '',
      antCoins: 0,
      orderType: 'purchase',
      amount: 0,
      remark: '',
    },
  });

  // 基于手机号查询用户应用列表
  const { data: userApps, isLoading: isLoadingApps, error: appsError } = useQuery({
    queryKey: ['userAppsByPhone', phoneNumber],
    queryFn: async () => {
      // 根据手机号查询用户ID，然后获取用户应用
      const userResponse = await getOpenPlatformUsers({ phone: phoneNumber });
      if (userResponse.data && userResponse.data.length > 0) {
        const userId = userResponse.data[0].id;
        return await getUserApps({ userId });
      }
      return { applications: [] };
    },
    enabled: phoneNumber.length === 11, // 只有当手机号为11位时才查询
    retry: false,
  });

  const mutation = useMutation({
    mutationFn: createRechargeOrder,
    onSuccess: () => {
      toast.success('充值订单创建成功');
      queryClient.invalidateQueries({ queryKey: ['rechargeOrders'] });
      form.reset();
      onClose();
    },
    onError: (error: any) => {
      toast.error(error?.message || '充值订单创建失败');
    },
  });

  const onSubmit = (data: FormData) => {
    mutation.mutate(data);
  };

  // 处理手机号变化
  const handlePhoneChange = (phone: string) => {
    setPhoneNumber(phone);
    // 如果手机号不是11位，清空应用选择
    if (phone.length !== 11) {
      form.setValue('appId', '');
    }
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      setPhoneNumber('');
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>创建充值订单</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>手机号 *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入手机号"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handlePhoneChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="appId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>应用筛选 *</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            phoneNumber.length !== 11
                              ? "请先输入完整手机号"
                              : isLoadingApps
                                ? "加载应用中..."
                                : userApps?.applications?.length === 0
                                  ? "该用户暂无应用"
                                  : "请选择应用"
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {userApps?.applications?.map((app) => (
                        <SelectItem key={app.appId} value={app.appId}>
                          {app.name} ({app.appId})
                        </SelectItem>
                      ))}
                      {userApps?.applications?.length === 0 && phoneNumber.length === 11 && !isLoadingApps && (
                        <div className="px-2 py-1 text-sm text-muted-foreground">
                          该用户暂无应用
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                  {appsError && (
                    <div className="text-sm text-red-500 mt-1">
                      查询应用失败，请重试
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="antCoins"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>蚁币数量 *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="请输入蚁币数量"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="orderType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>购买类型 *</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={field.onChange}
                      className="flex gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="purchase" id="purchase" />
                        <Label htmlFor="purchase">购买</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="gift" id="gift" />
                        <Label htmlFor="gift">赠送</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>付款金额</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="请输入付款金额"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入备注信息（可选）"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                创建订单
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
