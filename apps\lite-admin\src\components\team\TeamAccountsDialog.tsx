import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@mono/ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { ScrollArea } from '@mono/ui/scroll-area';
import { UserBase } from '@mono/ui/common/UserBase';
import { useQuery } from '@tanstack/react-query';
import { getTeamAccounts } from '@/api/team';
import { useState } from 'react';
import { getPlatformByName } from '@/lib/platforms';
import { LoadingContainer } from '../loading';
import { Button } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';

export const TeamAccountsDialog = ({
  children,
  id,
  code,
}: {
  children?: React.ReactNode;
  id: string;
  code: string;
}) => {
  const [open, setOpen] = useState(false);
  const query = useQuery({
    queryKey: ['getTeamAccounts', id],
    queryFn: () => getTeamAccounts(id),
    enabled: open,
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="!max-w-none w-[844px] p-0">
        <DialogTitle className="text-lg leading-6 font-medium pl-6 pt-[22px]">
          查看授权账号
        </DialogTitle>
        <VisuallyHidden>
          <DialogDescription />
        </VisuallyHidden>
        <ScrollArea
          style={{
            // maxHeight: 'calc(100vh - 80px)',
            maxHeight: '490px',
          }}
          className="overflow-hidden"
        >
          {query.isLoading && <LoadingContainer />}
          <div className="grid grid-cols-3 gap-4 px-6 pb-4">
            {query.data?.map((item, index) => (
              <div
                className="px-4 py-[14px] bg-muted border border-solid border-muted rounded-lg"
                key={index}
              >
                <UserBase
                  size="xl"
                  id={index}
                  name={item.platformAccountName}
                  avatar={item.platformAvatar}
                  platformComponent={
                    <img
                      src={
                        getPlatformByName(item.platformName, !!item.parentId)
                          .icon
                      }
                      className="w-5 h-5 rounded-full absolute -right-1 -bottom-1"
                    />
                  }
                />
              </div>
            ))}
          </div>
        </ScrollArea>
        <DialogFooter className="px-4 pb-4">
          <Link to="/platformAccount" search={{ teamName: code }}>
            <Button>账号明细</Button>
          </Link>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
