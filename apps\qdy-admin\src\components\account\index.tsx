import TableData from '@/components/table';
import { getTableColumns } from '@/components/account/columns';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { accountList, unAuthorize } from '@/api/account';
import { AccountTableSearch } from './AccountTableSearch';
import { AccountParams } from '@/types/account';
import { alertBaseManager } from '../alertBase';

export function Account() {
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useState<AccountParams>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const query = useQuery({
    queryKey: ['accountList', pagination, searchParams],
    queryFn: () =>
      accountList({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
        ...searchParams,
      }),
  });

  const unAuthorizeMutation = useMutation({
    mutationFn: unAuthorize,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['accountList'] });
    },
  });

  const columns = useMemo(() => {
    return getTableColumns((platformAccountId: number) => {
      alertBaseManager.open({
        title: '解除授权',
        description: '确定解除该账号授权？',
        onSubmit: () => {
          unAuthorizeMutation.mutate(platformAccountId);
        },
      });
    });
  }, []);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <AccountTableSearch
        onSearch={(values: AccountParams) => {
          setSearchParams(values);
          setPagination({
            pageIndex: 0,
            pageSize: 20,
          });
        }}
        values={searchParams}
      />

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
