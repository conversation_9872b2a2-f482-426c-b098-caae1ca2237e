// 封装 axios
import { getUserAuthorization, logout } from '@/composables/use-authorize';
import axios from 'axios';
import { AxiosResponse, AxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import { Response } from '@/types/index';

export const instance = axios.create({
  baseURL: import.meta.env.VITE_APP_API_URL,
  timeout: 30000, // 30秒无响应即超时处理
  headers: {
    'Content-Type': 'application/json',
  },
});

instance.interceptors.request.use((config) => {
  const accessToken = getUserAuthorization()?.accessToken;
  // const tokenType = getUserAuthorization()?.tokenType
  if (accessToken) {
    config.headers.setAuthorization(`${accessToken}`);
  }
  return config;
});

instance.interceptors.response.use(
  (response) => {
    const apiResponse = response.data;
    if (apiResponse.statusCode !== 0) {
      toast.error(apiResponse.message);
      return Promise.reject(apiResponse.message);
    } else {
      return response;
    }
  },
  (error) => {
    console.log(error);
    // 未登录
    if (error?.response?.status === 401) {
      // toast.error("请先登录");
      return logout();
    }
    toast.error(error?.response?.data?.message || error.message);
    return Promise.reject(error);
  }
);

// 通用请求函数
async function makeRequest<T>(config: AxiosRequestConfig): Promise<T> {
  const response: AxiosResponse<Response<T>> = await instance.request(config);
  return response.data.data; // 返回 Response 数据
}

export { makeRequest };
