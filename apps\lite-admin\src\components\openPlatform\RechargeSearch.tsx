import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';
import { DatePickerWithRange } from '@/components/DatePickerWithRange';
import { RechargeOrderParams } from '@/types/openPlatform';
import { Search, RotateCcw } from 'lucide-react';

interface RechargeSearchProps {
  onSearch: (params: RechargeOrderParams) => void;
  values: Partial<RechargeOrderParams>;
}

type SearchForm = {
  phone?: string;
  rechargeOrderNo?: string;
  appName?: string;
  appId?: string;
  rechargeType?: 'BANK_TRANSFER' | 'GIFT' | '';
  dateRange?: {
    from: Date;
    to: Date;
  };
};

export function RechargeSearch({ onSearch, values }: RechargeSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      phone: values.phone || '',
      rechargeOrderNo: values.rechargeOrderNo || '',
      appName: values.appName || '',
      appId: values.appId || '',
      rechargeType: values.rechargeType || '',
      dateRange:
        values.startTime && values.endTime ?
          {
            from: new Date(values.startTime),
            to: new Date(values.endTime),
          }
        : undefined,
    },
  });

  const handleSubmit = (data: SearchForm) => {
    const params: RechargeOrderParams = {
      phone: data.phone || undefined,
      rechargeOrderNo: data.rechargeOrderNo || undefined,
      appName: data.appName || undefined,
      appId: data.appId || undefined,
      rechargeType: data.rechargeType || undefined,
      startTime: data.dateRange?.from?.toISOString(),
      endTime: data.dateRange?.to?.toISOString(),
    };
    onSearch(params);
  };

  const handleReset = () => {
    form.reset({
      phone: '',
      rechargeOrderNo: '',
      appName: '',
      appId: '',
      rechargeType: '',
      dateRange: undefined,
    });
    onSearch({});
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="flex flex-wrap gap-4">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>手机号</FormLabel>
                <Input {...field} placeholder="请输入手机号" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rechargeOrderNo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>充值订单号</FormLabel>
                <Input {...field} placeholder="请输入充值订单号" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>应用名称</FormLabel>
                <Input {...field} placeholder="请输入应用名称" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>应用ID</FormLabel>
                <Input {...field} placeholder="请输入应用ID" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rechargeType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>订单类型</FormLabel>
                <Select
                  value={field.value !== '' ? String(field.value) : 'all'}
                  onValueChange={(value) =>
                    field.onChange(
                      value === 'all' ? undefined
                      : value === 'BANK_TRANSFER' ? 'BANK_TRANSFER'
                      : 'GIFT'
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择订单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="BANK_TRANSFER">购买</SelectItem>
                    <SelectItem value="GIFT">赠送</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dateRange"
            render={({ field }) => (
              <FormItem>
                <FormLabel>完成时间</FormLabel>
                <DatePickerWithRange
                  onChange={(dateRange) => {
                    field.onChange(
                      dateRange?.from && dateRange?.to ?
                        {
                          from: dateRange.from,
                          to: dateRange.to,
                        }
                      : undefined
                    );
                  }}
                />
              </FormItem>
            )}
          />

          <div className="flex items-end gap-2">
            <Button type="submit" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              搜索
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="w-4 h-4" />
              重置
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
