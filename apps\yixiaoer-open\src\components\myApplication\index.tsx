import { Button } from '@mono/ui/button';
import { CreateOrEditApplication } from './createOrEditApplication';
import TableData from '@/components/table';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useRef, useState } from 'react';
import { alertBaseManager } from '@/components/alertBase.tsx';
import { Application } from '@/types/application';
import { applicationList, deleteApplication } from '@/api/application';
import { getTableColumns } from './columns';
import { useNavigate } from '@tanstack/react-router';

export function MyApplication() {
  const [open, setOpen] = useState(false);
  const currentApplication = useRef<Application>();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const query = useQuery({
    queryKey: ['applicationList', pagination],
    queryFn: () =>
      applicationList({
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
  });

  const deleteMutation = useMutation({
    mutationFn: deleteApplication,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['applicationList'] });
    },
  });

  const columns = useMemo(() => {
    return getTableColumns(
      (item: Application) => {
        currentApplication.current = item;
        setOpen(true);
      },
      (id: string) => {
        alertBaseManager.open({
          isHideIcon: true,
          title: '确定删除应用吗?',
          description:
            '删除应用后,clientkey和secret将会被注销不可用,开放平台基于该等应用提供的能力将立即中断且不可恢复。请确认是否可删除,避免影响贵司业务异常',
          onSubmit: () => {
            deleteMutation.mutate(id);
          },
        });
      }
    );
  }, [deleteMutation]);

  return (
    <div className="w-full h-full flex flex-col gap-4 px-[94px] pt-6 overflow-hidden">
      <div className="flex items-center justify-between">
        <p>我的应用</p>
        <Button onClick={() => setOpen(true)}>创建应用</Button>
      </div>

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize ?? 0}
        pagination={pagination}
        setPagination={setPagination}
        onItemClick={(item: Application) => {
          // console.log('%c appId:\n', 'color:#FF7A45', item.appId);
          navigate({
            to: '/application/$applicationId/overview',
            params: { applicationId: item.id },
          });
        }}
      />

      <CreateOrEditApplication
        open={open}
        setOpen={setOpen}
        application={currentApplication.current}
      />
    </div>
  );
}
