import { useSearch } from '@tanstack/react-router';
import { ActivationRecordTableSearch } from './activationRecordTableSearch';
import { useState, useMemo } from 'react';
import { PaginationState } from '@tanstack/react-table';
import TableData from '@/components/table';
import { getTableColumns } from '@/components/coupon/activationRecordTableSearchColumns';
import { useQuery } from '@tanstack/react-query';
import { ActivationRecordParams } from '@/types/coupon';
import { sendRecordList } from '@/api/coupon';

export function ActivationRecordPage() {
  const { couponId } = useSearch({ strict: false });
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const [searchParams, setSearchParams] = useState<ActivationRecordParams>({
    status: -1,
  });

  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  const query = useQuery({
    queryKey: ['sendRecordList', pagination, searchParams],
    queryFn: () =>
      sendRecordList({
        ...searchParams,
        couponId: couponId ? couponId : 0,
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
  });

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <ActivationRecordTableSearch
        values={searchParams}
        onSearch={setSearchParams}
      />

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
