import { makeRequest } from '.';

// Webhook 配置类型定义
export interface WebhookConfig {
  id?: string;
  applicationId: string;
  webhookUrl: string;
}

export interface WebhookConfigRequest {
  webhookUrl: string;
  isEnabled?: boolean;
}

// 获取 Webhook 配置
// GET /open-platform/applications/{applicationId}/webhook
export const getWebhookConfig = (applicationId: string) => {
  return makeRequest<WebhookConfig>({
    url: `/open-platform/applications/${applicationId}/webhook`,
    method: 'GET',
  });
};

// 保存 Webhook 配置
// POST /open-platform/applications/{applicationId}/webhook
export const saveWebhookConfig = (
  applicationId: string,
  params: WebhookConfigRequest
) => {
  return makeRequest<WebhookConfig>({
    url: `/open-platform/applications/${applicationId}/webhook`,
    method: 'PUT',
    data: params,
  });
};

// 测试 Webhook 连接
// POST /open-platform/applications/{applicationId}/webhook/test
export const testWebhookConnection = (
  applicationId: string,
  webhookUrl: string
) => {
  return makeRequest<{
    success: boolean;
    message: string;
    responseTime?: number;
  }>({
    url: `/open-platform/applications/${applicationId}/webhook/test`,
    method: 'POST',
    data: { webhookUrl },
  });
};

// 删除 Webhook 配置
// DELETE /open-platform/applications/{applicationId}/webhook
export const deleteWebhookConfig = (applicationId: string) => {
  return makeRequest({
    url: `/open-platform/applications/${applicationId}/webhook`,
    method: 'DELETE',
  });
};
