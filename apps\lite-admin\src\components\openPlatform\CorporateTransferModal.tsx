import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Textarea } from '@mono/ui/textarea';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { setCorporateTransferStatus } from '@/api/openPlatform';
import { RechargeOrder } from '@/types/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface CorporateTransferModalProps {
  open: boolean;
  onClose: () => void;
  order: RechargeOrder | null;
}

type FormData = {
  remark?: string;
};

export function CorporateTransferModal({
  open,
  onClose,
  order,
}: CorporateTransferModalProps) {
  const queryClient = useQueryClient();
  const [actionType, setActionType] = useState<'enable' | 'disable' | null>(
    null
  );

  const form = useForm<FormData>({
    defaultValues: {
      remark: '',
    },
  });

  // 设置状态
  const mutation = useMutation({
    mutationFn: setCorporateTransferStatus,
    onSuccess: () => {
      toast.success(actionType === 'enable' ? '开通成功' : '取消开通成功');
      queryClient.invalidateQueries({ queryKey: ['corporateTransferStatus'] });
      queryClient.invalidateQueries({ queryKey: ['openPlatformOrders'] });
      onClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        (error as { message?: string })?.message || '操作失败';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    if (!order || !actionType) return;

    mutation.mutate({
      rechargeOrderNo: order.rechargeOrderNo,
      enable: actionType === 'enable',
      remark: data.remark,
    });
  };

  const handleAction = (type: 'enable' | 'disable') => {
    setActionType(type);
    // 自动提交表单
    form.handleSubmit(onSubmit)();
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      setActionType(null);
      onClose();
    }
  };

  useEffect(() => {
    if (open) {
      form.reset({ remark: '' });
      setActionType(null);
    }
  }, [open, form]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>对公转账开通</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 订单信息 */}
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">
              订单号：{order?.rechargeOrderNo}
            </div>
            <div className="text-sm text-muted-foreground">
              订单金额：¥{order?.paymentAmount.toFixed(2)}
            </div>
          </div>

          {/* 备注 */}
          <Form {...form}>
            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注（可选）</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="请输入操作备注"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </Form>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={mutation.isPending}
          >
            取消
          </Button>

          <Button
            type="button"
            onClick={() => handleAction('enable')}
            disabled={mutation.isPending}
          >
            {mutation.isPending && actionType === 'enable' && (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            )}
            开通
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
