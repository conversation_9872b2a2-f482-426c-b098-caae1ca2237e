import { Button } from '@mono/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { cn } from '@mono/utils';
import { CalendarIcon, X } from 'lucide-react';
import { formatYearMonthDay } from '@mono/utils/day';
import { Calendar } from '@mono/ui/calendar';
import { zhCN } from 'date-fns/locale';

export function CalendarRange({
  value,
  onChange,
  ...props
}: React.ComponentProps<typeof Calendar> & {
  value?: { from: Date; to?: Date };
  onChange?: (value?: { from?: Date; to?: Date }) => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'w-[230px] pl-2 justify-start text-left font-normal relative',
            !value && 'text-muted-foreground'
          )}
        >
          <CalendarIcon />
          {value ?
            value.to ?
              <>
                {formatYearMonthDay(value.from)} -{' '}
                {formatYearMonthDay(value.to)}
              </>
            : formatYearMonthDay(value.from)
          : <span>选择时间段</span>}
          {
            // 清空日期
            value && (
              <Button
                type="button"
                size="icon"
                variant="ghost"
                className="p-0 w-5 h-5  absolute top-[7px] right-2"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange?.();
                }}
              >
                <X className="h-4 w-4 opacity-50" />
              </Button>
            )
          }
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          {...props}
          locale={zhCN}
          mode="range"
          selected={value}
          onSelect={(date) => {
            if (!date) return;
            // 设置结束时间为23:59:59
            if (date.to) {
              date.to = new Date(new Date(date.to).setHours(23, 59, 59, 0));
            }
            onChange?.(date);
          }}
          numberOfMonths={2}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
