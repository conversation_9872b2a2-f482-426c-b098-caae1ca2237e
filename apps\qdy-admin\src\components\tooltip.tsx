import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from '@mono/ui/tooltip';

const SauryTooltip = ({
  children,
  tooltip,
  side = 'top',
  ...props
}: {
  children: React.ReactNode;
  tooltip: React.ReactNode;
  side?: React.ComponentProps<typeof TooltipContent>['side'];
} & React.ComponentProps<typeof Tooltip>) => (
  <TooltipProvider delayDuration={200}>
    <Tooltip {...props}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side={side} className="max-w-96">
        {tooltip}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

export default SauryTooltip;
