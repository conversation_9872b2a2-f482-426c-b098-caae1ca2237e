{"name": "coozf-web-apps", "version": "1.0.0", "description": "", "private": true, "scripts": {"ui-add": "pnpm -F @mono/ui ui-add", "lite-dev": "pnpm run --filter lite-admin dev", "qdy-dev": "pnpm run --filter qdy-admin dev", "yixiaoer-open-dev": "pnpm run --filter yixia<PERSON>r-open dev", "prepare": "husky", "lint-staged": "lint-staged", "tsc": "tsc -p packages/config-ts/tsconfig.json --noEmit --skipLibCheck"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --fix", "npx prettier --write"]}, "keywords": [], "dependencies": {"class-variance-authority": "0.7.0", "clsx": "2.1.0", "lucide-react": "0.372.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "2.3.0", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@types/node": "^20.12.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.19", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.18", "husky": "9.1.7", "lint-staged": "15.4.1", "postcss": "8.4.38", "prettier": "3.5.2", "tailwindcss": "3.4.3", "typescript": "^5.7.3", "vite": "^5.2.0"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "esbuild"]}}