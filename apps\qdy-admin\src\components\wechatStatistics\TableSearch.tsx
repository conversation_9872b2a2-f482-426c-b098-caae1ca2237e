import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem } from '@mono/ui/form';
import { omitBy } from 'lodash';
import { WechatSearchParams } from '@/types/wechat';
import { CalendarPopover } from '../CalendarPopover';
import { Button } from '@mono/ui/button';
import { format } from 'date-fns';

const formSchema = z.object({
  startTime: z.date().optional(),
  endTime: z.date().optional(),
});

export function TableSearch({
  values,
  onSearch,
}: {
  values?: WechatSearchParams;
  onSearch: (values: WechatSearchParams) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...values,
      startTime: values?.startTime ? new Date(values?.startTime) : undefined,
      endTime: values?.endTime ? new Date(values?.endTime) : undefined,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: WechatSearchParams = {
      startTime:
        values.startTime?.getTime() ?
          format(values.startTime?.getTime() ?? new Date(), 'yyyy-MM-dd')
        : undefined,
      endTime:
        values.endTime?.getTime() ?
          format(values.endTime?.getTime(), 'yyyy-MM-dd')
        : undefined,
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 justify-start p-0.5"
      >
        <FormField
          control={form.control}
          name="startTime"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <CalendarPopover
                placeHolder="选择第一次登录时间"
                value={field.value}
                onChange={field.onChange}
              />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="endTime"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <CalendarPopover
                placeHolder="选择最后登录时间"
                value={field.value}
                onChange={field.onChange}
              />
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
        <Button
          variant="outline"
          onClick={() => {
            form.reset();
          }}
        >
          重置
        </Button>
      </form>
    </Form>
  );
}
