import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from '@mono/ui/sheet';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { Separator } from '@mono/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import { useQuery } from '@tanstack/react-query';
import { orderRecordList } from '@/api/team';

const types = {
  create: '开通VIP',
  upgrade: '升级/续费',
  diff: '补差',
  refund: '退费',
  expire: '到期',
};

export function FinancialRecords({
  open,
  setOpen,
  id,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  id: number;
}) {
  const query = useQuery({
    queryKey: ['orderRecordList', id],
    enabled: !!id,
    queryFn: () => orderRecordList(id),
  });

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent className="w-[584px] min-w-[584px] p-0">
        <SheetHeader className="p-6 pb-3">
          <SheetTitle>资金变动记录</SheetTitle>
          <VisuallyHidden>
            <SheetDescription></SheetDescription>
          </VisuallyHidden>
        </SheetHeader>

        <Separator orientation="horizontal" />

        <div className="flex flex-col flex-1 w-full h-full overflow-y-auto">
          <Table>
            <TableHeader>
              <TableRow className="sticky top-0 bg-muted z-10">
                <TableHead className="px-6">金额</TableHead>
                <TableHead className="px-6">类型</TableHead>
                <TableHead className="px-6">备注</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {query.data?.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="px-6">{item.price}</TableCell>
                  <TableCell className="px-6">
                    {item.price < 0 ? '支出' : '收入'}
                  </TableCell>
                  <TableCell className="px-6">
                    {(types as { [key: string]: string })[item.type]}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <VisuallyHidden>
          <SheetFooter>
            <SheetClose asChild></SheetClose>
          </SheetFooter>
        </VisuallyHidden>
      </SheetContent>
    </Sheet>
  );
}
