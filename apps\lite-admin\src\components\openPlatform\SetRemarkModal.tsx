import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { setUserRemark } from '@/api/openPlatform';
import { OpenPlatformUser } from '@/types/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  remark: z.string().max(500, '备注不能超过500个字符'),
});

type FormData = z.infer<typeof formSchema>;

interface SetRemarkModalProps {
  open: boolean;
  onClose: () => void;
  user: OpenPlatformUser | null;
}

export function SetRemarkModal({ open, onClose, user }: SetRemarkModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      remark: user?.remark || '',
    },
  });

  // 当用户数据变化时重置表单
  useState(() => {
    if (user) {
      form.reset({
        remark: user.remark || '',
      });
    }
  });

  const mutation = useMutation({
    mutationFn: setUserRemark,
    onSuccess: () => {
      toast.success('备注设置成功');
      queryClient.invalidateQueries({ queryKey: ['openPlatformUsers'] });
      onClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '备注设置失败';
      toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    if (!user) return;

    const payload = {
      userId: user.id,
      remark: data.remark,
    };
    mutation.mutate(payload);
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置备注</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                用户：{user?.phone}
              </div>

              <FormField
                control={form.control}
                name="remark"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormLabel className="whitespace-nowrap">备注</FormLabel>
                    <FormControl>
                      <textarea
                        className="w-full border border-gray-300 rounded-md p-2"
                        rows={3}
                        placeholder="请输入备注"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                确认
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
