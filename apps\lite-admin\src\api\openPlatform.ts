import { makeRequest } from './index';
import { PageResult } from '@/types';
import {
  OpenPlatformUser,
  OpenPlatformUserParams,
  SetPriceParams,
  OpenPlatformOrder,
  OpenPlatformOrderParams,
  OpenPlatformOrderDetail,
  CorporateTransferStatus,
  CorporateTransferParams,
  SetRemarkParams,
} from '@/types/openPlatform';

// ==================== 用户管理 API ====================

/**
 * 获取开放平台用户列表
 * GET /open-platform/users
 */
export const getOpenPlatformUsers = (params: OpenPlatformUserParams) => {
  return makeRequest<PageResult<OpenPlatformUser>>({
    url: '/admin/open-platform/users',
    method: 'GET',
    params,
  });
};

/**
 * 获取开放平台用户详情
 * GET /open-platform/users/{id}
 */
export const getOpenPlatformUserDetail = (id: string) => {
  return makeRequest<OpenPlatformUser>({
    url: `/open-platform/users/${id}`,
    method: 'GET',
  });
};

/**
 * 设置用户价格
 * PUT /open-platform/users/{id}/price
 */
export const setUserPrice = (params: SetPriceParams) => {
  return makeRequest<void>({
    url: `/open-platform/users/${params.userId}/price`,
    method: 'PUT',
    data: {
      accountPrice: params.accountPrice,
      trafficPrice: params.trafficPrice,
    },
  });
};

/**
 * 设置用户备注
 * PUT /admin/open-platform/users/remark
 */
export const setUserRemark = (params: SetRemarkParams) => {
  return makeRequest<void>({
    url: '/admin/open-platform/users/remark',
    method: 'PUT',
    data: {
      userId: params.userId,
      remark: params.remark,
    },
  });
};

// ==================== 订单管理 API ====================

/**
 * 获取开放平台订单列表
 * GET /open-platform/orders
 */
export const getOpenPlatformOrders = (params: OpenPlatformOrderParams) => {
  return makeRequest<PageResult<OpenPlatformOrder>>({
    url: '/open-platform/orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取开放平台订单详情
 * GET /open-platform/orders/{id}
 */
export const getOpenPlatformOrderDetail = (id: string) => {
  return makeRequest<OpenPlatformOrderDetail>({
    url: `/open-platform/orders/${id}`,
    method: 'GET',
  });
};

/**
 * 获取对公转账开通状态
 * GET /open-platform/orders/{id}/corporate-transfer
 */
export const getCorporateTransferStatus = (orderId: string) => {
  return makeRequest<CorporateTransferStatus>({
    url: `/open-platform/orders/${orderId}/corporate-transfer`,
    method: 'GET',
  });
};

/**
 * 设置对公转账开通状态
 * PUT /open-platform/orders/{id}/corporate-transfer
 */
export const setCorporateTransferStatus = (params: CorporateTransferParams) => {
  return makeRequest<void>({
    url: `/open-platform/orders/${params.orderId}/corporate-transfer`,
    method: 'PUT',
    data: {
      enable: params.enable,
      remark: params.remark,
    },
  });
};

/**
 * 获取应用列表（用于订单筛选）
 * GET /open-platform/apps
 */
export const getOpenPlatformApps = () => {
  return makeRequest<Array<{ appId: string; name: string }>>({
    url: '/open-platform/apps',
    method: 'GET',
  });
};
