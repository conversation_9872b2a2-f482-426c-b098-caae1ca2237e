import { makeRequest } from './index';
import { PageResult } from '@/types';
import {
  OpenPlatformUser,
  OpenPlatformUserParams,
  SetPriceParams,
  OpenPlatformOrder,
  OpenPlatformOrderParams,
  OpenPlatformOrderDetail,
  CorporateTransferStatus,
  CorporateTransferParams,
  SetRemarkParams,
  OpenPlatformApp,
  OpenPlatformAppParams,
  SetAppPriceParams,
  ExtendedOpenPlatformOrder,
  ExtendedOpenPlatformOrderParams,
  RechargeOrder,
  RechargeOrderParams,
  CreateRechargeOrderParams,
} from '@/types/openPlatform';

// ==================== 用户管理 API ====================

/**
 * 获取开放平台用户列表
 * GET /open-platform/users
 */
export const getOpenPlatformUsers = (params: OpenPlatformUserParams) => {
  return makeRequest<PageResult<OpenPlatformUser>>({
    url: '/admin/open-platform/users',
    method: 'GET',
    params,
  });
};

/**
 * 获取开放平台用户详情
 * GET /open-platform/users/{id}
 */
export const getOpenPlatformUserDetail = (id: string) => {
  return makeRequest<OpenPlatformUser>({
    url: `/open-platform/users/${id}`,
    method: 'GET',
  });
};

/**
 * 设置用户价格
 * PUT /open-platform/users/{id}/price
 */
export const setUserPrice = (params: SetPriceParams) => {
  return makeRequest<void>({
    url: `/open-platform/users/${params.userId}/price`,
    method: 'PUT',
    data: {
      accountPrice: params.accountPrice,
      trafficPrice: params.trafficPrice,
    },
  });
};

/**
 * 设置用户备注
 * PUT /admin/open-platform/users/remark
 */
export const setUserRemark = (params: SetRemarkParams) => {
  return makeRequest<void>({
    url: '/admin/open-platform/users/remark',
    method: 'PUT',
    data: {
      userId: params.userId,
      remark: params.remark,
    },
  });
};

// ==================== 订单管理 API ====================

/**
 * 获取开放平台订单列表
 * GET /open-platform/orders
 */
export const getOpenPlatformOrders = (params: OpenPlatformOrderParams) => {
  return makeRequest<PageResult<OpenPlatformOrder>>({
    url: '/open-platform/orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取开放平台订单详情
 * GET /open-platform/orders/{id}
 */
export const getOpenPlatformOrderDetail = (id: string) => {
  return makeRequest<OpenPlatformOrderDetail>({
    url: `/open-platform/orders/${id}`,
    method: 'GET',
  });
};

/**
 * 获取对公转账开通状态
 * GET /open-platform/orders/{id}/corporate-transfer
 */
export const getCorporateTransferStatus = (orderId: string) => {
  return makeRequest<CorporateTransferStatus>({
    url: `/open-platform/orders/${orderId}/corporate-transfer`,
    method: 'GET',
  });
};

/**
 * 设置对公转账开通状态
 * PUT /open-platform/orders/{id}/corporate-transfer
 */
export const setCorporateTransferStatus = (params: CorporateTransferParams) => {
  return makeRequest<void>({
    url: `/open-platform/recharge/${params.rechargeOrderNo}/status`,
    method: 'PUT',
    data: {
      remark: params.remark,
    },
  });
};

/**
 * 获取应用列表（用于订单筛选）
 * GET /open-platform/apps
 */
export const getOpenPlatformApps = () => {
  return makeRequest<Array<{ appId: string; name: string }>>({
    url: '/open-platform/apps',
    method: 'GET',
  });
};

// ==================== 应用管理 API ====================

/**
 * 获取用户的应用列表
 * GET /admin/open-platform/users/{userId}/applications
 */
export const getUserApps = (params: OpenPlatformAppParams) => {
  return makeRequest<{ userPhone: string; applications: OpenPlatformApp[] }>({
    url: `/admin/open-platform/users/${params.userId}/applications`,
    method: 'GET',
    params: {
      appName: params.appName,
      appType: params.appType,
    },
  });
};

/**
 * 设置应用价格
 * PUT /admin/open-platform/apps/{appId}/price
 */
export const setAppPrice = (params: SetAppPriceParams) => {
  return makeRequest<void>({
    url: `/admin/open-platform/users/application/${params.id}/price`,
    method: 'PUT',
    data: {
      accountPrice: params.accountPrice,
      trafficPrice: params.trafficPrice,
      packagePrice: params.packagePrice,
    },
  });
};

// ==================== 充值管理 API ====================

/**
 * 获取充值订单列表
 * GET /admin/open-platform/recharge-orders
 */
export const getRechargeOrders = (params: RechargeOrderParams) => {
  return makeRequest<PageResult<RechargeOrder>>({
    url: '/open-platform/recharge',
    method: 'GET',
    params: {
      page: params.page,
      size: params.size,
      phone: params.phone,
      rechargeOrderNo: params.rechargeOrderNo,
      appName: params.appName,
      appId: params.appId,
      rechargeType: params.rechargeType,
      startTime: params.startTime,
      endTime: params.endTime,
    },
  });
};

/**
 * 获取充值订单详情
 * GET /admin/open-platform/recharge-orders/{orderId}
 */
export const getRechargeOrderDetail = (orderId: string) => {
  return makeRequest<RechargeOrder>({
    url: `/admin/open-platform/recharge-orders/${orderId}`,
    method: 'GET',
  });
};

/**
 * 获取应用列表（根据手机号码）
 * GET /admin/open-platform/users/applications
 */
export const getApplications = (phone?: string, name?: string) => {
  return makeRequest<{ userPhone: string; applications: OpenPlatformApp[] }>({
    url: '/admin/open-platform/users/applications',
    method: 'GET',
    params: {
      phone,
      name,
    },
  });
};

/**
 * 创建充值订单
 * POST /admin/open-platform/recharge-orders
 */
export const createRechargeOrder = (params: CreateRechargeOrderParams) => {
  return makeRequest<{ orderId: string }>({
    url: '/open-platform/recharge',
    method: 'POST',
    data: params,
  });
};

// ==================== 扩展订单管理 API ====================

/**
 * 获取扩展的开放平台订单列表
 * GET /admin/open-platform/orders/extended
 */
export const getExtendedOpenPlatformOrders = (
  params: ExtendedOpenPlatformOrderParams
) => {
  return makeRequest<PageResult<ExtendedOpenPlatformOrder>>({
    url: `/gateway/users/app/${params.appId}/orders`,
    method: 'GET',
    params,
  });
};

// 审核应用
// PUT /admin/open-platform/users/application/{id}/status
export const auditApplication = (params: { id: string; status: number }) => {
  return makeRequest({
    url: `/admin/open-platform/users/application/${params.id}/status`,
    method: 'PUT',
    data: { status: params.status },
  });
};
