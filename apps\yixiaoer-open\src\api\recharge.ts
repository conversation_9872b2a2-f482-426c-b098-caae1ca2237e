import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
import { RechargeRecord, RechargeParams } from '@/types/recharge';

// 获取充值记录列表
// GET /open-platform/recharge/records
export const rechargeList = (params: PageQuery & RechargeParams) => {
  return makeRequest<PageData<RechargeRecord>>({
    url: '/open-platform/recharge',
    method: 'GET',
    params,
  });
};

// 获取充值记录详情
// GET /open-platform/recharge/records/{id}
export const rechargeDetail = (id: string) => {
  return makeRequest<RechargeRecord>({
    url: `/open-platform/recharge/records/${id}`,
    method: 'GET',
  });
};

// 申请发票
// POST /open-platform/recharge/invoice
export const applyInvoice = (rechargeId: string) => {
  return makeRequest({
    url: '/open-platform/recharge/invoice',
    method: 'POST',
    data: { rechargeId },
  });
};

// 获取发票申请二维码
// GET /open-platform/recharge/invoice/qrcode
export const getInvoiceQRCode = () => {
  return makeRequest<{ qrCodeUrl: string }>({
    url: '/open-platform/recharge/invoice/qrcode',
    method: 'GET',
  });
};
