import { cn } from '@/lib/utils';
import { LoaderCircle } from 'lucide-react';

type LoadingProps = React.ComponentProps<typeof LoaderCircle>;
export function Loading({ className, ...props }: LoadingProps) {
  return (
    <LoaderCircle
      {...props}
      className={cn('w-5 h-5 animate-spin text-muted-foreground', className)}
    />
  );
}

export function LoadingContainer(props: LoadingProps) {
  return (
    <div className="flex-1 flex justify-center items-center">
      <Loading {...props} />
    </div>
  );
}
