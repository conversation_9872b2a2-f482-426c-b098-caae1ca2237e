import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { isNil, omitBy } from 'lodash';
import { PlatformAccountParams } from '@/types/platformAccount';
import { CustomSelect } from '@mono/ui/common/CustomSelect';
import { loginStatus } from './columns';
import { useEffect } from 'react';

type SearchForm = Omit<
  PlatformAccountParams,
  'createEndTime' | 'createStartTime'
> & {
  createDateRange?: {
    from: Date;
    to: Date;
  };
};

export function TableSearch({
  values,
  onSearch,
}: {
  values?: PlatformAccountParams;
  onSearch: (values: PlatformAccountParams) => void;
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      ...values,
    },
  });

  function onSubmit(values: SearchForm) {
    const params: PlatformAccountParams = {
      teamName: values.teamName?.trim(),
      platformName: values.platformName?.trim(),
      createStartTime: values?.createDateRange?.from?.getTime(),
      createEndTime: values.createDateRange?.to?.getTime(),
      platformAccountName: values.platformAccountName?.trim(),
      loginStatus: values.loginStatus,
    };
    const paramsOmitEmpty = omitBy(params, isNil);
    onSearch(paramsOmitEmpty);
  }

  const urlParams = new URLSearchParams(window.location.search);
  const paramValue = urlParams.get('teamName');
  useEffect(() => {
    if (paramValue) {
      form.register('teamName');
      form.setValue('teamName', paramValue);
      onSearch(form.getValues());
    }
  }, [paramValue]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="platformAccountName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>名称</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="账号名称/备注名称"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">团队</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="团队名称/id" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">创建时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="platformName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>平台</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="平台名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="loginStatus"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="whitespace-nowrap">登录状态</FormLabel>
              <FormControl>
                <CustomSelect
                  options={loginStatus}
                  value={field.value}
                  onChange={field.onChange}
                  includeAll
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
