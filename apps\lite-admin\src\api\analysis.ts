import { PageData, PageQuery } from '@/types';
import { makeRequest } from '.';
import { AnalysisData, AnalysisReq, TeamStatistic } from '@/types/analysis';
// 团队转化率/续费率
//   GET /overviews/team/rate
//   接口ID：261240064
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-261240064
export const getTeamRate = (query: PageQuery & AnalysisReq) => {
  return makeRequest<PageData<TeamStatistic> & AnalysisData>({
    url: '/overviews/team/rate',
    method: 'GET',
    params: query,
  });
};
