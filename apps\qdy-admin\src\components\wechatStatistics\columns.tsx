import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Button } from '@mono/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
import { Wechat } from '@/types/wechat.ts';

export function getTableColumns(
  onViewLoginHistory?: (items: Wechat) => void
): Array<ColumnDef<Wechat>> {
  return [
    {
      header: '视频号',
      accessorKey: 'wechat',
      cell: ({ row }) => {
        const { nickName, headImgUrl } = row.original;
        return (
          <div className="flex items-center gap-3">
            <Avatar className="w-9 h-9">
              <AvatarImage src={headImgUrl} />
              <AvatarFallback className="text-xs">{nickName}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-foreground">{nickName}</span>
            </div>
          </div>
        );
      },
    },
    {
      header: '微信号',
      accessorKey: 'wx',
      cell: ({ row }) => {
        const { wxid } = row.original;
        return <span className="text-muted-foreground">{wxid}</span>;
      },
    },
    {
      header: '设备ID',
      accessorKey: 'appId',
      cell: ({ row }) => {
        return <span>{row.original.appId}</span>;
      },
    },
    {
      header: '第一次登录时间',
      accessorKey: 'createTime',
      cell: ({ row }) => {
        const { createTime } = row.original;
        return (
          <span className="text-muted-foreground">
            {formatDate(createTime)}
          </span>
        );
      },
    },
    {
      header: '最后登录时间',
      accessorKey: 'loginTime',
      cell: ({ row }) => {
        const { loginTime } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(loginTime)}</span>
        );
      },
    },
    {
      header: '登录地区',
      accessorKey: 'regionId',
      cell: ({ row }) => {
        const { regionId, regionName } = row.original;
        return (
          <span>
            {regionName}
            {regionId}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            className="!p-0"
            onClick={() => onViewLoginHistory?.(row.original)}
          >
            查看历史登录
          </Button>
        );
      },
    },
  ];
}
