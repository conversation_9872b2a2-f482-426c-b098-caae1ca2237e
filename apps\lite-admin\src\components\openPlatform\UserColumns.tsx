import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { OpenPlatformUser } from '@/types/openPlatform';
import { format } from 'date-fns';
import { Settings } from 'lucide-react';

interface UserColumnsProps {
  onSetRemark: (user: OpenPlatformUser) => void;
  onViewApps: (user: OpenPlatformUser) => void;
}

export function getUserColumns({
  onSetRemark,
  onViewApps,
}: UserColumnsProps): ColumnDef<OpenPlatformUser>[] {
  return [
    {
      accessorKey: 'id',
      header: '用户ID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('id')}</div>
      ),
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('phone')}</div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: '注册时间',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'applicationCount',
      header: '应用数',
      cell: ({ row }) => {
        const user = row.original;
        const count = row.getValue('applicationCount') as number;

        return (
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-blue-600 hover:text-blue-800"
            onClick={() => onViewApps(user)}
          >
            {count}
          </Button>
        );
      },
    },
    {
      accessorKey: 'remark',
      header: '备注',
      cell: ({ row }) => {
        return <div className="font-medium">{row.getValue('remark')}</div>;
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSetRemark(user)}
              className="flex items-center gap-1"
            >
              <Settings className="w-4 h-4" />
              编辑
            </Button>
          </div>
        );
      },
    },
  ];
}
