// import { Search } from "lucide-react";
import { Button } from '@mono/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
// import { Input } from "@mono/ui/input";
import { ModeToggle } from './ModeToggle';
import { userInfo, userLogout } from '@/api/user';
import { useMutation, useQuery } from '@tanstack/react-query';
import { logout } from '@/lib/utils/auth';
import { Avatar, AvatarFallback, AvatarImage } from '@mono/ui/avatar';
// import { HeaderBread } from "./HeaderBread";

export function Profile() {
  const query = useQuery({
    queryKey: ['userInfo'],
    queryFn: userInfo,
  });
  const mutation = useMutation({
    mutationFn: userLogout,
    onSuccess: () => {
      logout();
    },
    onError: (error) => {
      // 登录失败
      console.log(error);
    },
  });
  const handleLogout = () => {
    mutation.mutate();
  };
  return (
    <div className="flex items-center justify-between">
      {/* <HeaderBread></HeaderBread>
      <div className="relative ml-auto flex-1 md:grow-0">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search..."
          className="w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[320px]"
        />
      </div> */}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="overflow-hidden rounded-full flex justify-between pl-0 pr-4 w-32 gap-1"
          >
            <Avatar className="w-[35px] h-[35px]">
              <AvatarImage src={query.data?.avatar} alt="@shadcn" />
              <AvatarFallback>
                {query.data?.username.toUpperCase().substring(0, 2)}
              </AvatarFallback>
            </Avatar>
            <span className="truncate">{query.data?.username}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>我的账户</DropdownMenuLabel>
          {/* <DropdownMenuItem>Settings</DropdownMenuItem>
          <DropdownMenuItem>Support</DropdownMenuItem> */}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>退出登录</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ModeToggle />
    </div>
  );
}
