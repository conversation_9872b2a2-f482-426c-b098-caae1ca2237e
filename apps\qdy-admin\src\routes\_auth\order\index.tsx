import { OrderPage } from '@/components/order';
import { createFileRoute } from '@tanstack/react-router';

type Search = {
  channelId?: number;
  orderNo?: string;
  salesType?: string;
  orderStartTime?: number;
  orderEndTime?: number;
  orderStatus?: string;
  invitationCode?: string;
};

export const Route = createFileRoute('/_auth/order/')({
  validateSearch: (search: Record<string, unknown>): Search => {
    return {
      channelId: search?.channelId as number,
      orderNo: search?.orderNo as string,
      salesType: search?.salesType as string,
      orderStartTime: search?.orderStartTime as number,
      orderEndTime: search?.orderEndTime as number,
      orderStatus: search?.orderStatus as string,
      invitationCode: search?.invitationCode as string,
    };
  },
  component: OrderPage,
});
