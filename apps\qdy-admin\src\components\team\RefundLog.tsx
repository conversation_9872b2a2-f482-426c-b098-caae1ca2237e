import { formatPrice } from '@/lib/utils';
import { Row } from '../order/detail';
import { OrderRefundTable } from './OrderRefundTable';
import { RefundRecord } from '@/types/team';
import { formatDate } from '@/lib/utils/day';
import { ScrollArea } from '@mono/ui/scroll-area';

export const RefundLog = ({ data }: { data: RefundRecord[] }) => {
  return (
    <ScrollArea className="flex gap-4 px-6 flex-col max-h-[calc(90vh-60px)] overflow-hidden">
      {data.map((item) => (
        <div className="flex flex-col gap-4">
          <OrderRefundTable data={item.orderInfo} />
          <Row label="应退费" value={formatPrice(item.actualRefundAmount)} />
          <Row label="实际退费" value={formatPrice(item.refundAmount)} />
          <Row label="退费时间" value={formatDate(item.createTime)} />
        </div>
      ))}
    </ScrollArea>
  );
};
