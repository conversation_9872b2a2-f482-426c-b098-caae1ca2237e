import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@mono/ui/dialog';
import { Button } from '@mono/ui/button';

import { LoadingButton } from '../loadingButton';

import { AppVersion } from '@/types/app';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteAppVersion } from '@/api/app';

export function SetDelete({
  open,
  setOpen,
  item,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  item?: AppVersion;
}) {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: deleteAppVersion,
    onSuccess: () => {
      setOpen(false);
      queryClient.refetchQueries({ queryKey: ['getAppVersions'] });
    },
    onError: (error) => {
      console.log(error);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="!max-w-none w-[300px] h-[120px]">
        <DialogHeader>
          <DialogTitle className="text-center">
            删除{item?.version}版本
          </DialogTitle>
        </DialogHeader>

        <DialogFooter className="flex !justify-between">
          <Button
            className="flex-1"
            onClick={() => {
              setOpen(false);
            }}
            variant="outline"
          >
            取消
          </Button>
          <LoadingButton
            className="flex-1"
            onClick={() => {
              if (!item) return;
              mutation.mutate(item._id);
            }}
          >
            确认删除
          </LoadingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
