import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createUser } from '@/api/user';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const usernameRegex = /^(?=.*[A-Za-z])(?=.*\d).+$/;

const formSchema = z.object({
  username: z.string().min(1, { message: '请输入账号' }).regex(usernameRegex, {
    message: '账号格式必须是：英文+数字',
  }),
  password: z
    .string()
    .min(8, '密码至少8位字符')
    .max(20, '密码不能超过20位字符'),
});

type FormData = z.infer<typeof formSchema>;

interface AddUserModalProps {
  open: boolean;
  onClose: () => void;
  applicationId: string;
}

export function AddUserModal({
  open,
  onClose,
  applicationId,
}: AddUserModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (data: FormData) => createUser(applicationId, data),
    onSuccess: () => {
      toast.success('用户添加成功');
      mutation.isPending = false;
      queryClient.invalidateQueries({ queryKey: ['memberList'] });
      handleClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '添加用户失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>添加新用户</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>账号 *</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入账号" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>密码 *</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="请输入密码（8-20位字符）"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                确认添加
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
