import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { createUser } from '@/api/user';
import { getInterest } from '@/api/order';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { useMemo } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';

const usernameRegex = /^[a-zA-Z0-9]+$/;

const formSchema = z.object({
  username: z.string().min(1, { message: '请输入账号' }).regex(usernameRegex, {
    message: '账号格式英文或数字',
  }),
  password: z
    .string()
    .min(8, '密码至少8位字符')
    .max(20, '密码不能超过20位字符'),
  interestCount: z.number().min(1, { message: '请输入权益包数量' }),
  interestId: z.string().min(1, { message: '请选择权益' }),
  month: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface AddUserModalProps {
  open: boolean;
  onClose: () => void;
  applicationId: string;
}

export function AddUserModal({
  open,
  onClose,
  applicationId,
}: AddUserModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      password: '',
      interestCount: 1,
      interestId: '',
      month: '',
    },
  });

  const vipQuery = useQuery({
    queryFn: () =>
      getInterest().then((res) => {
        form.setValue('interestId', `${res.id}`);
        return res;
      }),
    queryKey: ['getInterest'],
    enabled: open,
  });

  const vipOften = useMemo(() => {
    return vipQuery.data?.vipOften?.filter((item) => item.mount === 1) || [];
  }, [vipQuery.data]);

  const mutation = useMutation({
    mutationFn: (data: FormData) =>
      createUser(applicationId, { ...data, month: Number(data.month) }),
    onSuccess: () => {
      toast.success('用户添加成功');
      mutation.isPending = false;
      queryClient.invalidateQueries({ queryKey: ['memberList'] });
      handleClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '添加用户失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>添加新用户</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>账号 *</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入账号" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>密码 *</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="请输入密码（8-20位字符）"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="interestCount"
              render={({ field }) => (
                <FormItem className="flex items-center gap-1 space-y-0">
                  <FormLabel className="w-24 flex-shrink-0">
                    权益包数量
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={9999}
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="month"
              render={({ field }) => (
                <FormItem className="flex items-center gap-1 space-y-0">
                  <FormLabel className="w-24 flex-shrink-0">时长</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择时长" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {vipOften.map((item) => (
                        <SelectItem
                          key={item.mount}
                          value={`${item.mount}`}
                        >{`${item.mount}个月${item.present ? `+送${item.present}个月` : ''}`}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                确认添加
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
