import React, { ChangeEvent } from 'react';
import { Input } from '@mono/ui/input';

type NumberInputProps = {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
} & React.ComponentPropsWithoutRef<typeof Input>;

const NumberInput: React.FC<NumberInputProps> = ({
  value,
  onChange,
  placeholder,
  min,
  max,
  ...rest
}) => {
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
      const numericValue = newValue === '' ? 0 : parseInt(newValue, 10);
      if (
        (min !== undefined && numericValue < min) ||
        (max !== undefined && numericValue > max)
      ) {
        return;
      }
      onChange(numericValue);
    }
  };

  return (
    <Input
      type="text"
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      className="number-input"
      {...rest}
    />
  );
};

export default NumberInput;
