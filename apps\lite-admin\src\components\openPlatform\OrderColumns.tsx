import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import { OpenPlatformOrder, orderTypeMap, orderStatusMap, orderStatusStyleMap } from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye, CreditCard } from 'lucide-react';

interface OrderColumnsProps {
  onViewDetail: (order: OpenPlatformOrder) => void;
  onCorporateTransfer: (order: OpenPlatformOrder) => void;
}

export function getOrderColumns({
  onViewDetail,
  onCorporateTransfer,
}: OrderColumnsProps): ColumnDef<OpenPlatformOrder>[] {
  return [
    {
      accessorKey: 'orderNo',
      header: '订单号',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('orderNo')}</div>
      ),
    },
    {
      accessorKey: 'appId',
      header: 'AppID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('appId')}</div>
      ),
    },
    {
      accessorKey: 'orderType',
      header: '订单类型',
      cell: ({ row }) => {
        const type = row.getValue('orderType') as keyof typeof orderTypeMap;
        return (
          <Badge variant="outline">
            {orderTypeMap[type]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'amount',
      header: '金额',
      cell: ({ row }) => {
        const amount = row.getValue('amount') as number;
        return (
          <div className="text-sm font-medium">
            ¥{amount.toFixed(2)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as keyof typeof orderStatusMap;
        return (
          <Badge className={orderStatusStyleMap[status]}>
            {orderStatusMap[status]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: '用户手机号',
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue('phone')}</div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: '下单时间',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const order = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetail(order)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              详情
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCorporateTransfer(order)}
              className="flex items-center gap-1"
            >
              <CreditCard className="w-4 h-4" />
              对公转账开通
            </Button>
          </div>
        );
      },
    },
  ];
}
