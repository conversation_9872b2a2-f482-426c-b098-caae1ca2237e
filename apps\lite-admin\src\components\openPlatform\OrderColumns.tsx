import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import { ExtendedOpenPlatformOrder, orderTypeMap, orderStatusMap, orderStatusStyleMap, purchaseTypeMap, purchaseTypeStyleMap } from '@/types/openPlatform';
import { format } from 'date-fns';
import { Eye, CreditCard } from 'lucide-react';

interface OrderColumnsProps {
  onViewDetail: (order: ExtendedOpenPlatformOrder) => void;
  onCorporateTransfer: (order: ExtendedOpenPlatformOrder) => void;
}

export function getOrderColumns({
  onViewDetail,
  onCorporateTransfer,
}: OrderColumnsProps): ColumnDef<ExtendedOpenPlatformOrder>[] {
  return [
    {
      accessorKey: 'orderNo',
      header: '订单号',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('orderNo')}</div>
      ),
    },
    {
      accessorKey: 'appId',
      header: 'AppID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('appId')}</div>
      ),
    },
    {
      accessorKey: 'teamInfo',
      header: '团队信息',
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue('teamInfo') || '-'}</div>
      ),
    },
    {
      accessorKey: 'teamId',
      header: '团队ID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">{row.getValue('teamId')}</div>
      ),
    },
    {
      accessorKey: 'orderType',
      header: '订单类型',
      cell: ({ row }) => {
        const type = row.getValue('orderType') as keyof typeof orderTypeMap;
        return (
          <Badge variant="outline">
            {orderTypeMap[type]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'purchaseType',
      header: '购买类型',
      cell: ({ row }) => {
        const type = row.getValue('purchaseType') as keyof typeof purchaseTypeMap;
        return (
          <Badge className={purchaseTypeStyleMap[type]}>
            {purchaseTypeMap[type]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'amount',
      header: '金额',
      cell: ({ row }) => {
        const amount = row.getValue('amount') as number;
        return (
          <div className="text-sm font-medium">
            ¥{amount.toFixed(2)}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as keyof typeof orderStatusMap;
        return (
          <Badge className={orderStatusStyleMap[status]}>
            {orderStatusMap[status]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: '用户手机号',
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue('phone')}</div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'paymentTime',
      header: '支付时间',
      cell: ({ row }) => {
        const date = row.getValue('paymentTime') as string;
        return (
          <div className="text-sm">
            {date ? format(new Date(date), 'yyyy-MM-dd HH:mm:ss') : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'consumedAntCoins',
      header: '消耗蚁币',
      cell: ({ row }) => {
        const coins = row.getValue('consumedAntCoins') as number;
        return (
          <div className="text-sm font-medium text-orange-600">
            {coins.toLocaleString()}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const order = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetail(order)}
              className="flex items-center gap-1"
            >
              <Eye className="w-4 h-4" />
              详情
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCorporateTransfer(order)}
              className="flex items-center gap-1"
            >
              <CreditCard className="w-4 h-4" />
              对公转账开通
            </Button>
          </div>
        );
      },
    },
  ];
}
