import {
  ScrollText,
  Megaphone,
  UserRoundCog,
  Rocket,
  ClipboardList,
  Users,
  User,
  ChartNoAxesCombined,
  Share2,
  TrendingUp,
  Globe,
  Activity,
  ChartLine,
  Settings,
  ShoppingCart,
  MailQuestion,
} from 'lucide-react';

// 导航项类型
export interface NavItem {
  name: string;
  icon: React.ReactNode;
  path: string;
  isAdmin?: boolean;
  children?: NavItem[];
}

// 导航数据
export const navs: NavItem[] = [
  {
    name: '用户列表',
    icon: <User className="w-5 h-5" />,
    path: '/clientUser',
  },
  {
    name: '团队列表',
    icon: <Users className="w-5 h-5" />,
    path: '/team',
  },
  {
    name: '媒体账号',
    icon: <Globe className="w-5 h-5" />,
    path: '/platformAccount',
  },
  {
    name: '订单管理',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/order',
  },
  {
    name: '业绩分析',
    icon: <TrendingUp className="w-5 h-5" />,
    path: '/performance-analysis',
  },
  {
    name: '数据',
    icon: <ChartNoAxesCombined className="w-5 h-5" />,
    path: '/data',
  },
  {
    name: '净增账号趋势',
    icon: <ChartLine className="h-5 w-5" />,
    path: '/platform-statistics',
  },
  {
    name: '登录保持统计',
    icon: <Activity className="h-5 w-5" />,
    path: '/online-statistics',
  },
  {
    name: '渠道管理',
    icon: <Share2 className="w-5 h-5" />,
    path: '/channel',
  },
  {
    name: '广告管理',
    icon: <Megaphone className="w-5 h-5" />,
    path: '/ad',
  },
  {
    name: '系统公告',
    icon: <Megaphone className="w-5 h-5" />,
    path: '/systemNotify',
  },
  {
    name: '常见问题',
    icon: <MailQuestion className="w-5 h-5" />,
    path: '/question',
  },
  {
    name: '脚本更新',
    icon: <ScrollText className="w-5 h-5" />,
    path: '/updateScript',
  },
  {
    name: '版本管理',
    icon: <Rocket className="w-5 h-5" />,
    path: '/version',
  },
  {
    name: '开放平台',
    icon: <Settings className="w-5 h-5" />,
    path: '/open-platform',
    children: [
      {
        name: '用户管理',
        icon: <User className="w-4 h-4" />,
        path: '/open-platform/users',
      },
      {
        name: '充值管理',
        icon: <ShoppingCart className="w-4 h-4" />,
        path: '/open-platform/recharge',
      },
    ],
  },
  {
    name: '账号管理',
    icon: <UserRoundCog className="w-5 h-5" />,
    path: '/adminUser',
    isAdmin: true,
  },
  {
    name: '日志下载',
    icon: <UserRoundCog className="w-5 h-5" />,
    path: '/logs-download',
    isAdmin: true,
  },
];
