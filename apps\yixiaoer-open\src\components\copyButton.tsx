import { CopyIcon } from '@radix-ui/react-icons';
import { But<PERSON> } from '@mono/ui/button';
import { toast } from 'sonner';

type Props = React.ComponentProps<typeof Button> & {
  // 复制的文本
  text: string;
};

export function CopyButton(props: Props) {
  // 复制方法
  const copy = () => {
    navigator.clipboard.writeText(props.text);
    toast.success('复制成功');
  };
  return (
    <Button onClick={copy} variant="ghost" size="icon" {...props}>
      <span className="sr-only">Copy</span>
      <CopyIcon className="h-4 w-4" />
    </Button>
  );
}
