import { Button } from '@mono/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Textarea } from '@mono/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { corporateOrder, orderDetail } from '@/api/order.ts';

interface CorporateModalProps {
  orderId: string;
  open: boolean;
  onChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CorporateModal({
  open,
  orderId,
  onSuccess,
  onChange,
}: CorporateModalProps) {
  const formSchema = z.object({
    payAmount: z
      .string()
      .optional()
      .refine((value) => Number(value) >= 0, {
        message: '应付金额不能小于0',
      }),
    remark: z.string().optional(),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      payAmount: '0',
      remark: '',
    },
  });

  const mutation = useMutation({
    mutationFn: () =>
      corporateOrder(orderId, {
        payAmount: Number(form.getValues('payAmount')),
        remark: form.getValues('remark') || '',
      }),
    onSuccess: () => {
      onSuccess();
      onChange(false);
    },
  });

  function onSubmit() {
    mutation.mutate();
  }

  // 获取订单详情
  const query = useQuery({
    queryKey: ['getDetail'],
    queryFn: () => orderDetail(orderId),
  });

  const onHandel = () => {
    form.handleSubmit(onSubmit)();
  };

  return (
    <Dialog open={open} onOpenChange={onChange}>
      <DialogTrigger asChild></DialogTrigger>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle>开通对公转账</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="mt-5 grid grid-cols-1 gap-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
              <FormItem className={'flex gap-2 items-center'}>
                <FormLabel className={'whitespace-nowrap'}>
                  权益包数量：
                </FormLabel>
                <FormControl className={'!m-0'}>
                  <div>{query.data?.interestCount}</div>
                </FormControl>
                <FormMessage />
              </FormItem>

              <FormItem className={'flex gap-2 items-center'}>
                <FormLabel className={'whitespace-nowrap'}>
                  开通时长：
                </FormLabel>
                <FormControl className={'!m-0'}>
                  {query.data?.vipMonth === 0 ?
                    <div>{`${query.data.days}天`}</div>
                  : <div>{query.data?.vipMonth}个月</div>}
                </FormControl>
                <FormMessage />
              </FormItem>

              {query.data && query.data.freeMonth > 0 && (
                <FormItem className={'flex gap-2 items-center'}>
                  <FormLabel className={'whitespace-nowrap'}>
                    赠送时长：
                  </FormLabel>
                  <FormControl className={'!m-0'}>
                    <div>{query.data?.freeMonth}个月</div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}

              <FormItem className={'flex gap-2 items-center'}>
                <FormLabel className={'whitespace-nowrap'}>
                  订单金额：
                </FormLabel>
                <FormControl className={'!m-0'}>
                  <div>￥{query.data?.totalAmount}</div>
                </FormControl>
                <FormMessage />
              </FormItem>

              <FormItem className={'flex gap-2 items-center'}>
                <FormLabel className={'whitespace-nowrap'}>
                  应付金额：
                </FormLabel>
                <FormControl className={'!m-0'}>
                  <div>￥{query.data?.payableAmount}</div>
                </FormControl>
                <FormMessage />
              </FormItem>


              <FormField
                control={form.control}
                name="remark"
                render={({ field }) => (
                  <FormItem className={'flex gap-2'}>
                    <FormLabel className={'whitespace-nowrap'}>
                      备注（选填）：
                    </FormLabel>
                    <FormControl className={'!m-0'}>
                      <Textarea
                        {...field}
                        className="resize-none"
                        placeholder={'请输入备注'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button variant={'outline'} onClick={() => onChange(false)}>
            取消
          </Button>
          <Button
            disabled={mutation.isPending}
            variant={'default'}
            onClick={() => onHandel()}
          >
            开通
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
