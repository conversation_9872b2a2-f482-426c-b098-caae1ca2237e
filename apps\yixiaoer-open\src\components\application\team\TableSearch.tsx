import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { omitBy } from 'lodash';
import { TeamsReq } from '@/types/team';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';

type SearchForm = Pick<TeamsReq, 'teamName' | 'isVip'>;

export function TableSearch({
  values,
  onSearch,
}: {
  values?: TeamsReq;
  onSearch: (values: TeamsReq) => void;
}) {
  const { setValue, ...form } = useForm<SearchForm>({
    defaultValues: {
      teamName: values?.teamName?.trim() ?? '',
      isVip: values?.isVip,
    },
  });

  function onSubmit(values: SearchForm) {
    const params: TeamsReq = {
      teamName: values.teamName?.trim(),
      isVip: values.isVip,
    };
    const paramsOmitEmpty = omitBy(
      params,
      (value) => value === undefined || value === null || value === ''
    );
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form setValue={setValue} {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>团队名称</FormLabel>
              <FormControl>
                <Input
                  className="w-48"
                  placeholder="输入团队名称、ID搜索"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isVip"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>VIP</FormLabel>
              <Select
                onValueChange={(value) =>
                  field.onChange(value === 'all' ? undefined : value === 'vip')
                }
                defaultValue={
                  field.value === undefined ? 'all'
                  : field.value ?
                    'vip'
                  : 'unVip'
                }
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  {/* VIP */}
                  <SelectItem value="vip">已开通</SelectItem>
                  {/* 非VIP */}
                  <SelectItem value="unVip">未开通</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
