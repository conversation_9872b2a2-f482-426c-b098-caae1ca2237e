import TableData from '@/components/table';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { getTableColumns } from './columns';
import { InvoiceModal } from './InvoiceModal';
import { RechargeRecord } from '@/types/recharge';
import { useRechargeList } from '@/hooks/useRechargeList';

export function RechargeManagement() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const [selectedRecord, setSelectedRecord] = useState<RechargeRecord | null>(
    null
  );
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);

  const query = useRechargeList({
    size: pagination.pageSize,
    page: pagination.pageIndex + 1,
  });

  const columns = useMemo(() => {
    return getTableColumns((record: RechargeRecord) => {
      setSelectedRecord(record);
      setInvoiceModalOpen(true);
    });
  }, []);

  const handleCloseInvoiceModal = () => {
    setInvoiceModalOpen(false);
    setSelectedRecord(null);
  };

  return (
    <div className="w-full h-full flex flex-col gap-4 px-[94px] pt-6 overflow-hidden">
      <div className="flex items-center justify-between">
        <p className="text-[18px] text-[#222222] font-medium">充值管理</p>
      </div>

      <TableData
        columns={columns}
        data={query.data?.data || []}
        rowCount={query.data?.totalSize ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />

      <InvoiceModal
        open={invoiceModalOpen}
        onClose={handleCloseInvoiceModal}
        record={selectedRecord}
      />
    </div>
  );
}
