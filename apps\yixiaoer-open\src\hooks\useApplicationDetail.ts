import { applicationDetail, applicationOverview } from '@/api/application';
import { useParams } from '@tanstack/react-router';
import { useQuery } from '@tanstack/react-query';

export const useApplicationDetail = () => {
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  return useQuery({
    queryKey: ['applicationDetail', applicationId],
    queryFn: () => applicationDetail(applicationId as string),
    enabled: !!applicationId,
  });
};

export const useApplicationOverview = () => {
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  return useQuery({
    queryKey: ['applicationOverview', applicationId],
    queryFn: () => applicationOverview(applicationId as string),
    enabled: !!applicationId,
  });
};
