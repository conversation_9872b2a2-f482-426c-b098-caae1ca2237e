import { DataTable } from '@/components/dataTable';
import { getTableColumns } from '@/components/team/columns';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useState, useRef } from 'react';
import { teamList } from '@/api/team';
import { Team, TeamsReq } from '@/types/team';
import { TeamSetting } from '@/components/team/setting';
import { Refund } from '@/components/team/Refund';
import { RefundRecord } from '@/components/team/RefundRecord';
import { WebSetting } from '@/components/team/WebSetting';
import { TableSearch } from './TableSearch';
import { useSearch } from '@tanstack/react-router';

export function TeamPage() {
  const [settingOpen, setSettingOpen] = useState(false);
  const [refundOpen, setRefundOpen] = useState(false);
  const [refundRecordOpen, setRefundRecordOpen] = useState(false);
  const [webSettingOpen, setWebSettingOpen] = useState(false);
  const mTeam = useRef({} as Team);
  const {
    phone,
    teamCode,
    salesType,
    expiredEndTime,
    expiredStartTime,
    customerId,
  } = useSearch({ strict: false });

  const [filter, setFilter] = useState<TeamsReq>({
    phone: phone ? `${phone}` : '',
    teamName: teamCode ? `${teamCode}` : '',
    salesType: salesType !== undefined ? Number(salesType) : undefined,
    expiredStartTime: expiredStartTime ?? undefined,
    expiredEndTime: expiredEndTime ?? undefined,
    customerId,
  });

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo(() => {
    return getTableColumns(
      (item) => {
        mTeam.current = item;
        setRefundOpen(true);
      },
      (item) => {
        mTeam.current = item;
        setRefundRecordOpen(true);
      },
      (item) => {
        mTeam.current = item;
        setWebSettingOpen(true);
      }
    );
  }, []);

  const query = useQuery({
    queryKey: ['teamList', pagination, filter],
    queryFn: () =>
      teamList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      }),
  });

  return (
    <div className="flex flex-col w-full h-full gap-4 overflow-hidden">
      <TableSearch
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 });
          setFilter(value);
        }}
        values={filter}
      />
      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <TeamSetting
        team={mTeam.current}
        open={settingOpen}
        setOpen={setSettingOpen}
      />

      {refundOpen && (
        <Refund
          team={mTeam.current}
          open={refundOpen}
          onChange={setRefundOpen}
          onSuccess={() => {
            setRefundOpen(false);
            query.refetch();
          }}
        />
      )}

      {refundRecordOpen && (
        <RefundRecord
          teamId={mTeam.current.id}
          open={refundRecordOpen}
          onChange={setRefundRecordOpen}
        />
      )}

      {webSettingOpen && (
        <WebSetting
          team={mTeam.current}
          open={webSettingOpen}
          onChange={setWebSettingOpen}
        />
      )}
    </div>
  );
}
