import { makeRequest } from '@/api/index.ts';
import { IProxyType } from '@/types/proxyType.ts';
import { CategoryModelBase } from '@/components/CascaderSelect.tsx';

export const getProxyList = () => {
  return makeRequest<Array<IProxyType>>({
    url: `/kuaidaili/areas`,
    method: 'GET',
  });
};

export const updateProxyList = (
  id: string,
  data: Array<CategoryModelBase<never>>
) => {
  return makeRequest<IProxyType>({
    url: `/kuaidaili/${id}/areas`,
    method: 'PUT',
    data,
  });
};

export const getTeamProxyList = (teamId: string) => {
  return makeRequest<Array<IProxyType>>({
    url: `/kuaidaili/${teamId}/areas`,
    method: 'GET',
  });
};
