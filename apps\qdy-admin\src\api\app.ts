import { <PERSON>Data, <PERSON>Query } from '@/types';
import { makeRequest } from '.';
import {
  AppPlatformType,
  AppVersion,
  AppVersionParams,
  ICreateAppVersion,
  ICreateUploadUrlResponse,
} from '@/types/app';
import axios from 'axios';

// 获取App versions
export const getAppVersions = (params: PageQuery & AppVersionParams) => {
  return makeRequest<PageData<AppVersion>>({
    url: '/public/app-versions',
    method: 'GET',
    params,
  });
};

export function getUploadImageUrl(data: { type: string; objectName: string }) {
  return makeRequest<ICreateUploadUrlResponse>({
    url: '/public/upload-urls',
    method: 'POST',
    data: data,
  });
}

export function getAppLastVersion(type: AppPlatformType) {
  return makeRequest<AppVersion>({
    url: '/public/last-app-version',
    method: 'GET',
    params: {
      type,
    },
  });
}

export function createAppVersion(data: Omit<ICreateAppVersion, 'file'>) {
  return makeRequest<AppVersion>({
    url: '/public/app-versions',
    method: 'POST',
    data: data,
  });
}

export function deleteAppVersion(id: string) {
  return makeRequest({
    url: `/public/app-versions/${id}`,
    method: 'DELETE',
  });
}

export const ossUpload = async (
  file: File,
  filename: string,
  onProgress: (value: number) => void
) => {
  try {
    const result = await getUploadImageUrl({
      type: 'app',
      objectName: filename,
    });

    const formData = new FormData();
    formData.append('policy', result.policy);
    formData.append('x-tos-algorithm', result['x-tos-algorithm']);
    formData.append('x-tos-credential', result['x-tos-credential']);
    formData.append('x-tos-date', result['x-tos-date']);
    formData.append('x-tos-signature', result['x-tos-signature']);
    formData.append('key', result.key);
    formData.append('file', file);
    formData.append('name', filename);

    await axios({
      url: result.host,
      method: 'POST',
      data: formData,
      onUploadProgress: (progressEvent) => {
        const percent = Math.floor(
          (progressEvent.loaded * 100) / progressEvent.total!
        );
        onProgress(percent);
      },
    });
    return `https://image.qdy.com/${result.key}`;
  } catch (error) {
    console.error('发生错误:', error);
  }
};
