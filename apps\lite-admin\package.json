{"name": "lite-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"ui-add": "pnpm -F @mono/ui ui-add", "dev": "vite", "build": "tsc && vite build --mode production", "build:dev": "tsc && vite build --mode dev", "build:pre": "tsc && vite build --mode staging", "build:prod": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@mono/hooks": "workspace:*", "@mono/ui": "workspace:*", "@mono/utils": "workspace:*", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-use-controllable-state": "1.1.0", "@radix-ui/react-visually-hidden": "^1.1.0", "@tanstack/react-query": "^5.51.11", "@tanstack/react-router": "^1.45.10", "@tanstack/react-table": "^8.19.3", "ali-oss": "^6.22.0", "axios": "^1.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "3.6.0", "dayjs": "^1.11.12", "echarts": "5.6.0", "echarts-for-react": "3.0.2", "embla-carousel-react": "^8.5.2", "immer": "10.1.1", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.416.0", "next-themes": "^0.3.0", "oidc-client-ts": "^3.0.1", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-photo-view": "1.2.7", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-immer": "0.11.0", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^4.5.4"}, "devDependencies": {"@mono/config-tailwind": "workspace:*", "@mono/config-ts": "workspace:*", "@tanstack/react-query-devtools": "^5.51.11", "@tanstack/router-devtools": "^1.45.10", "@tanstack/router-plugin": "^1.45.8", "@types/ali-oss": "^6.16.11", "@types/lodash": "^4.17.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.7.0"}}