# 充值管理功能

## 功能概述

充值管理页面提供了完整的充值记录查看和发票申请功能，用户可以查看所有充值记录并申请发票。

## 文件结构

```
src/components/rechargeManagement/
├── RechargeManagement.tsx    # 主页面组件
├── columns.tsx               # 表格列定义
├── InvoiceModal.tsx         # 申请发票弹窗
├── index.ts                 # 导出文件
└── README.md               # 说明文档

src/types/recharge.ts        # 充值相关类型定义
src/api/recharge.ts          # 充值相关API接口
src/hooks/useRechargeList.ts # 充值列表数据hook
src/routes/_auth/recharge-management.tsx # 路由配置
```

## 功能特性

### 1. 充值记录列表

- 显示应用名称和订单号
- 显示购买数量、付款金额、付款方式
- 显示充值类型和状态
- 显示完成时间
- 支持分页查看

### 2. 申请发票功能

- 只有充值成功的记录才能申请发票
- 点击"申请发票"按钮弹出模态框
- 显示充值详情和二维码
- 提供客服联系方式

### 3. 状态管理

- 待支付：黄色标签
- 充值成功：绿色标签
- 充值失败：红色标签
- 处理中：蓝色标签

## 使用方法

### 1. 访问页面

从用户头像下拉菜单中点击"充值管理"即可进入页面。

### 2. 查看充值记录

页面会自动加载用户的充值记录，支持分页浏览。

### 3. 申请发票

对于充值成功的记录，点击"申请发票"按钮，扫描二维码联系客服。

## API 接口

### 获取充值记录列表

```typescript
GET /open-platform/recharge/records
参数：
- page: 页码
- size: 每页数量
- applicationId: 应用ID（可选）
- status: 充值状态（可选）
```

### 获取发票申请二维码

```typescript
GET /open-platform/recharge/invoice/qrcode
返回：
- qrCodeUrl: 二维码图片URL
```

## 类型定义

主要类型包括：

- `RechargeRecord`: 充值记录
- `PaymentMethod`: 支付方式
- `RechargeType`: 充值类型
- `RechargeStatus`: 充值状态

## 样式说明

页面采用与"我的应用"页面相同的布局风格：

- 使用 shadcn/ui 组件库
- 响应式设计，支持不同屏幕尺寸
- 统一的颜色和间距规范
