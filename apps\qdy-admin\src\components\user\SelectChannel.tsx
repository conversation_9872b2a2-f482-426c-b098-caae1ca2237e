'use client';

import * as React from 'react';
import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';

import { cn } from '@/lib/utils';
import { Button } from '@mono/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@mono/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { debounce } from 'lodash';
import { LoadingContainer } from '@/components/loading.tsx';
import { getChannels } from '@/api/channel';

export function SelectChannel({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const [open, setOpen] = React.useState(false);
  const [searchText, setSearchText] = React.useState('');
  const channelsQuery = useQuery({
    queryFn: () =>
      getChannels({
        name: searchText,
        size: 1000,
      }),
    queryKey: ['getChannels', searchText],
    // enabled: !!searchText,
  });

  // TODO: 优化搜索
  const searchFun = useMemo(
    () => debounce(setSearchText, 300),
    [setSearchText]
  );
  const currentChannel = useMemo(() => {
    if (value) {
      return channelsQuery.data?.data.find((item) => `${item.id}` === value);
    }
    return undefined;
  }, [value, channelsQuery.data?.data]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between whitespace-normal"
        >
          {currentChannel ?
            <span className="line-clamp-1 flex-1 text-start">
              {currentChannel.name}
            </span>
          : <>选择渠道</>}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[200px]">
        <Command shouldFilter={false}>
          <CommandInput
            onChangeCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
              searchFun(e.target.value);
            }}
            placeholder="输入渠道名称进行搜索"
            className="h-9"
          />
          <CommandList>
            {channelsQuery.isLoading ?
              <div className="py-6">
                <LoadingContainer className="w-5 h-5" />
              </div>
            : <CommandEmpty>No channels found</CommandEmpty>}
            <CommandGroup>
              {channelsQuery.data &&
                channelsQuery.data.data.map((team) => (
                  <CommandItem
                    key={team.id}
                    value={`${team.id}`}
                    className="w-full flex items-center gap-2 justify-between"
                    onSelect={(currentValue) => {
                      onChange(currentValue === value ? '' : currentValue);
                      setOpen(false);
                    }}
                  >
                    <span>{team.name}</span>
                    <CheckIcon
                      className={cn(
                        'ml-auto h-4 w-4',
                        value === `${team.id}` ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
