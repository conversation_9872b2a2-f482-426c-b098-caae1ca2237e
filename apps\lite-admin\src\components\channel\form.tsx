import { channelDetail, createChannel, updateChannel } from '@/api/channel';
import { ChannelFormType, channelformSchema } from '@/types/channel';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@mono/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { LoadingContainer } from '../loading';

interface ChannelFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  channelId?: string; // 编辑时的初始数据
  onSuccess?: () => void;
}

const defaultValues = {
  channelName: '',
  channelCode: '',
  username: '',
  password: '',
};

export function ChannelForm({
  open,
  onOpenChange,
  channelId,
  onSuccess,
}: ChannelFormProps) {
  const { reset, ...form } = useForm<ChannelFormType>({
    resolver: zodResolver(
      channelId ? channelformSchema.partial() : channelformSchema
    ),
    defaultValues,
    mode: 'onChange',
  });

  const query = useQuery({
    queryKey: ['channel', channelId],
    queryFn: () => channelDetail(channelId!),
    enabled: !!channelId,
    staleTime: 0,
  });

  useEffect(() => {
    if (query.data) {
      reset(query.data);
    }
  }, [query.data, reset]);

  const createMutation = useMutation({
    mutationFn: createChannel,
    onSuccess: () => {
      onOpenChange(false);
      onSuccess?.();
    },
  });

  const updateMutation = useMutation({
    mutationFn: (value: ChannelFormType) => updateChannel(channelId!, value),
    onSuccess: () => {
      onOpenChange(false);
      onSuccess?.();
    },
  });

  const onSubmit = (values: ChannelFormType) => {
    if (channelId) {
      updateMutation.mutate(values);
    } else {
      createMutation.mutate(values);
    }
  };

  useEffect(() => {
    if (open && !channelId) {
      reset(defaultValues);
    }
  }, [channelId, open, reset]);
  const title = channelId ? '编辑渠道' : '添加渠道';
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="default">添加渠道</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription />
        </DialogHeader>
        {query.isError && <div>查询错误</div>}
        {query.isLoading && <LoadingContainer />}
        {(!channelId || query.isSuccess) && (
          <Form {...form} reset={reset}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
              <FormField
                control={form.control}
                name="channelName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>渠道名称</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="channelCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>渠道码</FormLabel>
                    <FormControl>
                      <Input disabled={!!channelId} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>渠道账号</FormLabel>
                    <FormControl>
                      <Input disabled={!!channelId} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>渠道密码</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="giftDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>赠送天数</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        value={field.value?.toString() ?? ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === '') {
                            field.onChange(undefined);
                          } else {
                            field.onChange(Number(value));
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end pt-4">
                <Button type="submit">提交</Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
