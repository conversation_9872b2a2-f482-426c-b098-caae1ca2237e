import { useMemo, useRef, useState } from 'react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@mono/ui/button';
import { Calendar } from '@mono/ui/calendar';
import { getTableColumns } from '@/components/version/columns';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { createFileRoute } from '@tanstack/react-router';
import { DataTable } from '@/components/dataTable';
import { Desktop, DesktopDetail, OS } from '@/types/desktopVersion';
import { PaginationState } from '@tanstack/react-table';
import {
  deleteDesktop,
  desktopVersionList,
  getDesktopLatest,
} from '@/api/desktop';
import { DesktopSetting } from '@/components/version/setting';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@mono/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@mono/ui/alert-dialog';
import { Input } from '@mono/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@mono/ui/select';

export const Route = createFileRoute('/_home/version')({
  component: DesktopVersion,
});

function DesktopVersion() {
  const queryClient = useQueryClient();

  const [desktopType, setDesktopType] = useState<OS>(OS.Windows);
  const [open, setOpen] = useState(false);
  const [latest, setLatest] = useState('');
  const [dialog, setDialog] = useState(false);
  const [date, setDate] = useState<DateRange | undefined>();
  const [isPopoverOpen, setPopoverOpen] = useState(false);
  const mDesktop = useRef({} as DesktopDetail);
  const [version, setVersion] = useState<undefined | string>(undefined);
  const [platform, setPlatform] = useState<undefined | string>('all');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // 搜索逻辑
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 重置分页
    query.refetch();
  };

  const handelLatest = async (type: OS) => {
    const latestResult = await getDesktopLatest(type);
    setLatest(latestResult?.version ?? '-');
  };
  const columns = useMemo(() => {
    return getTableColumns((desktop: Desktop, method: string) => {
      mDesktop.current = desktop;
      setDesktopType(desktop.type);
      if (method == 'edit') {
        handelLatest(desktop.type);
        setOpen(true);
      }
      if (method == 'delete') {
        setDialog(true);
      }
    });
  }, []);

  const query = useQuery({
    queryKey: ['desktopList', pagination],
    queryFn: () =>
      desktopVersionList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        publishStartTime: date?.from,
        publishEndTime: date?.to,
        version: version,
        type: platform,
      }),
  });

  const handelDelete = async (id: string) => {
    await deleteDesktop(id);
    await queryClient.refetchQueries({
      queryKey: ['desktopList'],
    });
  };
  const handleMenuItemClick = (option: OS) => {
    mDesktop.current = {
      id: '',
      requestUrl:
        option == OS.IOS ?
          'https://apps.apple.com/us/app/%E8%9A%81%E5%B0%8F%E4%BA%8Clite/id6737591220'
        : '',
      type: '',
      version: '',
      isForce: false,
      notice: '',
    };

    handelLatest(option);
    setDesktopType(option);
    setOpen(true); // 打开弹框
  };

  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      <div className="flex pt-1 pb-4 gap-2 px-1">
        <Popover open={isPopoverOpen} onOpenChange={setPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={'outline'}
              className={cn(
                'w-[250px] justify-start text-left font-normal',
                !date && 'text-muted-foreground'
              )}
              onClick={() => setPopoverOpen(true)} // 打开日历
            >
              {date?.from ?
                date.to ?
                  <>
                    {format(date.from, 'yyyy-MM-dd')} -{' '}
                    {format(date.to, 'yyyy-MM-dd')}
                  </>
                : format(date.from, 'yyyy-MM-dd')
              : <span>选择日期</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={(selectedDate) => setDate(selectedDate)}
              numberOfMonths={2}
              locale={zhCN}
            />
          </PopoverContent>
        </Popover>

        <Input
          className="w-40"
          placeholder="版本号"
          onChange={(e) => {
            setVersion(e.target.value);
          }}
        />

        <Select
          onValueChange={(e) => {
            setPlatform(e);
          }}
          defaultValue={platform}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="请选择平台" />
          </SelectTrigger>
          <SelectContent className={'w-48'}>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="windows">Windows</SelectItem>
            <SelectItem value="macos">MacOS</SelectItem>
            <SelectItem value="ios">苹果</SelectItem>
            <SelectItem value="android">安卓</SelectItem>
          </SelectContent>
        </Select>

        <Button type="button" onClick={handleSearch}>
          搜索
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button>添加版本</Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => handleMenuItemClick(OS.Windows)}>
                <span>Windows</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleMenuItemClick(OS.MacOS)}>
                <span>MacOS</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleMenuItemClick(OS.IOS)}>
                <span>苹果</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleMenuItemClick(OS.Android)}>
                <span>安卓</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      <DesktopSetting
        desktopType={desktopType}
        desktop={mDesktop.current}
        open={open}
        setOpen={setOpen}
        latest={latest}
      />

      <AlertDialog open={dialog} onOpenChange={setDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确定删除吗?</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handelDelete(mDesktop.current.id)}
            >
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
