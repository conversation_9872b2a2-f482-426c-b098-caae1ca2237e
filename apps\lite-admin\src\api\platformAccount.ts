import { makeRequest } from './index';
import { PageData, PageQuery } from '@/types/index';
import {
  PlatformAccount,
  PlatformAccountParams,
} from '@/types/platformAccount';

// 媒体账号列表
//   GET /platform-account
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const platformAccountList = (
  params: PlatformAccountParams & PageQuery
) => {
  return makeRequest<PageData<PlatformAccount>>({
    url: '/platform-account',
    method: 'GET',
    params,
  });
};
