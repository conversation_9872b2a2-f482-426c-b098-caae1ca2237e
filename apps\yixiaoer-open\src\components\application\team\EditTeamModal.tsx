import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateTeamInfo } from '@/api/team';
import { Team } from '@/types/team';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  teamName: z
    .string()
    .min(1, '团队名称不能为空')
    .max(50, '团队名称不能超过50个字符')
    .trim(),
});

type FormData = z.infer<typeof formSchema>;

interface EditTeamModalProps {
  open: boolean;
  onClose: () => void;
  team: Team | null;
}

export function EditTeamModal({ open, onClose, team }: EditTeamModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      teamName: '',
    },
  });

  // 当团队数据变化时重置表单
  useEffect(() => {
    if (team && open) {
      form.reset({
        teamName: team.name || '',
      });
    }
  }, [team, open, form]);

  const mutation = useMutation({
    mutationFn: updateTeamInfo,
    onSuccess: () => {
      toast.success('团队信息更新成功');
      queryClient.invalidateQueries({ queryKey: ['teamList'] });
      handleClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '团队信息更新失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    if (!team) return;

    mutation.mutate({
      teamId: team.id,
      teamName: data.teamName,
    });
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset({
        teamName: '',
      });
    }

    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>编辑团队</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 团队信息显示 */}
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">团队信息</div>
              <div className="p-4 bg-muted/50 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">团队ID：</span>
                  <span className="text-sm font-mono">{team?.code}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">创建时间：</span>
                  <span className="text-sm">
                    {team?.createdAt ?
                      new Date(team.createdAt).toLocaleString('zh-CN')
                    : '-'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">团队成员：</span>
                  <span className="text-sm">{team?.memberCount || 0} 人</span>
                </div>
              </div>
            </div>

            {/* 团队名称编辑 */}
            <FormField
              control={form.control}
              name="teamName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>团队名称 *</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入团队名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                保存
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
