{"name": "@mono/ui", "version": "1.0.0", "description": "", "scripts": {"ui-add": "pnpx shadcn-ui@latest add"}, "keywords": [], "dependencies": {"@hookform/resolvers": "^3.10.0", "@mono/config-ts": "workspace:*", "@mono/utils": "workspace:*", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "class-variance-authority": "0.7.1", "cmdk": "1.0.0", "date-fns": "3.6.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "0.471.1", "next-themes": "^0.3.0", "react-day-picker": "8.10.1", "react-hook-form": "^7.54.2", "react-photo-view": "1.2.7", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "sonner": "^1.7.1", "vaul": "^1.1.2", "zod": "^3.24.1"}, "exports": {"./*": "./src/components/ui/*.tsx", "./common/*": "./src/components/*.tsx"}, "devDependencies": {"@mono/config-tailwind": "workspace:*"}}