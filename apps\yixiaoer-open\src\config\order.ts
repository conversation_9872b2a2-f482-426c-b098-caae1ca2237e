import { OrderStatus, SaleType } from '@/types/order';

export const orderStatusMap: Record<OrderStatus, string> = {
  pending: '待支付',
  success: '已完成',
  canceled: '已取消',
  refund: '已退费',
};

export const saleTypeMap: Record<SaleType, string> = {
  NotBuy: '未购买',
  FirstBuy: '新购',
  ReBuy: '复购',
  Buy: '已购买',
};

export const orderTypeMap: Record<string, string> = {
  open_platform_traffic: '流量',
  open_platform_account_points: '账号',
  interest_package: '权益包',
};
