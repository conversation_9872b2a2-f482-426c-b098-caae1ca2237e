import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@mono/ui/button';
import { Badge } from '@mono/ui/badge';
import {
  RechargeRecord,
  rechargeTypeMap,
  rechargeStatusMap,
  rechargeStatusStyleMap,
} from '@/types/recharge';
import { formatNumber } from '@/lib/utils';
import { format } from 'date-fns';

export function getTableColumns(
  onApplyInvoice: (record: RechargeRecord) => void
): Array<ColumnDef<RechargeRecord>> {
  return [
    {
      header: '充值信息',
      accessorKey: 'applicationName',
      cell: ({ row }) => {
        const { applicationName, rechargeOrderNo } = row.original;
        return (
          <div className="flex flex-col gap-1">
            <span className="font-medium text-[#222222]">
              {applicationName}
            </span>
            <span className="text-sm text-[#757575]">
              订单号: {rechargeOrderNo}
            </span>
          </div>
        );
      },
    },
    {
      header: '购买数量',
      accessorKey: 'rechargeAmount',
      cell: ({ row }) => {
        const { rechargeAmount } = row.original;
        return (
          <span className="text-[#222222] font-medium">
            {formatNumber(rechargeAmount)}
          </span>
        );
      },
    },
    {
      header: '付款金额',
      accessorKey: 'payAmount',
      cell: ({ row }) => {
        const { paymentAmount } = row.original;
        return (
          <span className="text-[#222222] font-medium">
            ¥{(paymentAmount / 100).toFixed(2)}
          </span>
        );
      },
    },
    {
      header: '充值类型',
      accessorKey: 'rechargeType',
      cell: ({ row }) => {
        const { rechargeType } = row.original;
        return (
          <span className="text-[#222222]">
            {rechargeTypeMap[rechargeType]}
          </span>
        );
      },
    },
    {
      header: '充值状态',
      accessorKey: 'status',
      cell: ({ row }) => {
        const { rechargeStatus } = row.original;
        return (
          <Badge
            variant="secondary"
            className={`${rechargeStatusStyleMap[rechargeStatus]} border-0 font-normal`}
          >
            {rechargeStatusMap[rechargeStatus]}
          </Badge>
        );
      },
    },
    {
      header: '完成时间',
      accessorKey: 'completedAt',
      cell: ({ row }) => {
        const { rechargeTime, rechargeStatus } = row.original;
        if (rechargeStatus !== 'SUCCESS' || !rechargeTime) {
          return <span className="text-[#757575]">-</span>;
        }
        return (
          <span className="text-[#222222]">
            {format(new Date(rechargeTime), 'yyyy-MM-dd HH:mm:ss')}
          </span>
        );
      },
    },
    {
      header: '操作',
      accessorKey: 'action',
      cell: ({ row }) => {
        const record = row.original;
        const { rechargeStatus } = record;

        // 只有充值成功的记录才能申请发票
        if (rechargeStatus !== 'SUCCESS') {
          return <span className="text-[#757575]">-</span>;
        }

        return (
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onApplyInvoice(record);
            }}
            className="text-[#4F46E5] border-[#4F46E5] hover:bg-[#4F46E5] hover:text-white"
          >
            申请发票
          </Button>
        );
      },
    },
  ];
}
