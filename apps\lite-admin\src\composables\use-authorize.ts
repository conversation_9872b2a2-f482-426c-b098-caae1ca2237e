import { router } from '@/router';
import { useUserStore } from '@/store/user';
import { Authorization } from '@/types/authorization';

/**
 * 授权登录成功，状态存储登录信息
 */
export const authorize = (data: Authorization, name: string) => {
  const userAuthorization = {
    accessToken: data.access_token,
    expiresIn: data.expires_at,
    refreshToken: data.refresh_token,
    tokenType: data.token_type,
  };

  const userInfo = {
    id: '',
    username: name ?? 'admin',
    avatar: '',
    role: 0,
  };

  useUserStore.setState({
    userInfo: userInfo,
    userAuthorization: userAuthorization,
  });
};

/**
 * 退出登录，移除登录信息
 */
export const logout = async () => {
  // await userManager.removeUser();
  useUserStore.setState({
    userInfo: undefined,
    userAuthorization: undefined,
  });
  router.navigate({ to: '/login', replace: true });
};

/**
 * 获取登录授权信息
 */
export const getUserAuthorization = () => {
  return useUserStore.getState().userAuthorization;
};

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return useUserStore.getState().userInfo;
};
