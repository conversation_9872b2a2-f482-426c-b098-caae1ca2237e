import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@/lib/utils/day';
import { Team } from '@/types/team';
import { Badge } from '@mono/ui/badge';
import { Button } from '@mono/ui/button';
import { Edit } from 'lucide-react';

interface TableColumnsProps {
  onEdit: (team: Team) => void;
}

export function getTableColumns({
  onEdit,
}: TableColumnsProps): Array<ColumnDef<Team>> {
  return [
    {
      header: '团队信息',
      accessorKey: 'teamInfo',
      cell: ({ row }) => {
        const { name, code } = row.original;
        return (
          <div className="flex flex-col text-muted-foreground">
            <span>团队名称：{name}</span>
            <span>团队ID：{code}</span>
          </div>
        );
      },
    },
    {
      header: '创建时间',
      accessorKey: '',
      cell: ({ row }) => {
        const { createdAt } = row.original;
        return (
          <span className="text-muted-foreground">{formatDate(createdAt)}</span>
        );
      },
    },
    {
      header: '授权账号',
      accessorKey: '',
      cell: ({ row }) => {
        const { accountCapacity, accountCapacityLimit } = row.original;
        return (
          <span className="text-muted-foreground">
            {accountCapacity}/{accountCapacityLimit}
          </span>
        );
      },
    },
    {
      header: '团队流量',
      accessorKey: '',
      cell: ({ row }) => {
        const { useNetworkTraffic, networkTraffic } = row.original;
        return (
          <span className="text-muted-foreground">
            {useNetworkTraffic}G/{networkTraffic}G
          </span>
        );
      },
    },
    {
      header: '团队成员',
      accessorKey: 'memberCount',
      cell: ({ row }) => {
        const { memberCount } = row.original;
        return <span className="text-muted-foreground">{memberCount}</span>;
      },
    },
    {
      header: 'VIP状态',
      accessorKey: 'enabled',
      cell: ({ row }) => {
        const { expiredAt } = row.original;
        const isExpired = expiredAt && new Date(expiredAt) > new Date(); // 检查 expiredAt 是否为 null 或有效
        return (
          <span>
            {isExpired ?
              <Badge variant="default">已开通</Badge>
            : <Badge variant="secondary">未开通</Badge>}
          </span>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const team = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(team)}
              className="flex items-center gap-1"
            >
              <Edit className="w-4 h-4" />
              编辑
            </Button>
          </div>
        );
      },
    },
  ];
}
