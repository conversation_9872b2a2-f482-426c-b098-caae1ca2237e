import { Input } from '@mono/ui/input';
import { cn } from '@/lib/utils';

export default function LengthInput({
  className,
  warpClassName,
  maxLength,
  onChange,
  ...props
}: React.InputHTMLAttributes<HTMLInputElement> & { warpClassName?: string }) {
  return (
    <div className={cn('relative ml-auto flex-1 p-[1px]', warpClassName)}>
      <Input
        className={cn('bg-background pr-14 w-full h-8', className)}
        onChange={(e) => {
          onChange?.(e);
        }}
        maxLength={maxLength}
        {...props}
      />
      <div className="absolute right-2 top-0 h-full flex items-center text-muted-foreground text-xs">
        {props.value?.toString().length}/{maxLength}
      </div>
    </div>
  );
}
