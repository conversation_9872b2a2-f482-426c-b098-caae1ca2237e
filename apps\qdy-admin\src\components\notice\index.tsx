import { Create } from '@/components/notice/create.tsx';
import { TableBase } from '@/components/notice/table.tsx';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteNotice } from '@/api/notice.ts';
import { useMemo, useState } from 'react';
import { getColumns } from '@/components/notice/columns.tsx';
import { alertBaseManager } from '@/components/alertBase.tsx';
import { Notify } from '@/types/notice.ts';

export function NoticePage() {
  const queryClient = useQueryClient();
  const [currentNotice, setCurrentNotice] = useState<Notify>();
  const deleteMutation = useMutation({
    mutationFn: deleteNotice,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['getNotices'] });
    },
  });
  const columns = useMemo(() => {
    return getColumns((item, type) => {
      if (type === 'update') {
        setCurrentNotice({ ...item });
      } else if (type === 'del') {
        alertBaseManager.open({
          title: '删除公告',
          description: '确定删除该公告吗？',
          onSubmit: () => {
            deleteMutation.mutate(item.id);
          },
        });
      }
    });
  }, [deleteMutation, setCurrentNotice]);
  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <div>
        <Create notice={currentNotice} />
      </div>
      <TableBase columns={columns} />
    </div>
  );
}
