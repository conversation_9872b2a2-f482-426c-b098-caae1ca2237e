import TableData from '@/components/table';
import { useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useRef, useState } from 'react';
import { teamList } from '@/api/team';
import { getTableColumns } from './columns';
import { TeamsReq } from '@/types/team';
import { TableSearch } from './TableSearch';
import { useParams } from '@tanstack/react-router';

export function ApplicationTeam() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });

  const searchParams = useRef<TeamsReq>({
    teamName: undefined,
    isVip: undefined,
  });

  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  const query = useQuery({
    queryKey: ['teamList', pagination, applicationId],
    queryFn: () =>
      teamList(applicationId as string, {
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
        ...searchParams.current,
      }),
    enabled: !!applicationId,
  });

  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  return (
    <div className="w-full h-full flex flex-col gap-3 px-[20px] py-5 overflow-hidden">
      <TableSearch
        onSearch={(value) => {
          searchParams.current = value;
          setPagination({ ...pagination, pageIndex: 0 });
          query.refetch();
        }}
        values={searchParams.current}
      />
      <TableData
        columns={columns}
        data={query.data?.data || []}
        rowCount={query.data?.totalSize ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
