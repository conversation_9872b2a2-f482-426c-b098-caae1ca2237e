import { PageResult } from '@/types';
import {
  giftParams,
  Interest,
  Order,
  OrderCreateParams,
  OrderDetail,
  OrderListParams,
  PriceSum,
  PriceSumParams,
  RefundList,
  RefundOutput,
  RefundParams,
  RefundRecord,
  renewParams,
  upgradeParams,
} from '@/types/order.ts';
import { makeRequest } from '@/api/index.ts';

export const orderList = (params: OrderListParams) => {
  return makeRequest<PageResult<Order> & { totalAmount: number }>({
    url: '/orders',
    method: 'GET',
    params,
  });
};

export const orderDetail = (orderId: string) => {
  return makeRequest<OrderDetail>({
    url: `/orders/${orderId}`,
    method: 'GET',
  });
};

export const orderCreate = (params: OrderCreateParams) => {
  return makeRequest<Order>({
    url: '/orders',
    method: 'POST',
    data: params,
  });
};

export const getInterest = () => {
  return makeRequest<Interest>({
    url: '/orders/interest',
    method: 'GET',
  });
};

export const getPriceSum = (params: PriceSumParams) => {
  return makeRequest<PriceSum>({
    url: '/orders/price',
    method: 'POST',
    data: params,
  });
};

// 续费
export const renewOrder = (params: renewParams) => {
  return makeRequest<Order>({
    url: '/orders/renew',
    method: 'POST',
    data: params,
  });
};

// 升级
export const upgradeOrder = (params: upgradeParams) => {
  return makeRequest<Order>({
    url: '/orders/upgrade',
    method: 'POST',
    data: params,
  });
};
// 赠送
export const giftOrder = (params: giftParams) => {
  return makeRequest<Order>({
    url: '/orders/gift',
    method: 'POST',
    data: params,
  });
};

// 退费订单列表
export const refundOrder = (teamId: string) => {
  return makeRequest<Array<RefundRecord>>({
    url: `/teams/${teamId}/refund/orders`,
    method: 'GET',
  });
};

// 退费确定
export const refundOrderById = (teamId: string, params: RefundParams) => {
  return makeRequest<RefundOutput>({
    url: `/teams/${teamId}/refund`,
    method: 'POST',
    data: params,
  });
};

// 退费记录
export const refundRecord = (teamId: string) => {
  return makeRequest<Array<RefundList>>({
    url: `/teams/${teamId}/refund`,
    method: 'GET',
  });
};

// 开通对公转账
export const corporateOrder = (
  orderNo: string,
  params: { payAmount: number; remark: string }
) => {
  return makeRequest<Order>({
    url: `/orders/${orderNo}/status`,
    method: 'PATCH',
    data: params,
  });
};

// 取消订单
export const cancelOrder = (orderNo: string) => {
  return makeRequest<Order>({
    url: `/orders/${orderNo}/cancel`,
    method: 'PATCH',
  });
};
