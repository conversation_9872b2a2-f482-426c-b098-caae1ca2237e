import TimeTabs from '@/components/overview/timeTabs';
import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@mono/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { Badge } from '@mono/ui/badge';
import { useQuery } from '@tanstack/react-query';
import { getTopTen } from '@/api/overview';
import { ScrollArea } from '@mono/ui/scroll-area';
import { DauChart } from './dauChart';
import { IncomeChart } from './incomeChart';
import { RegisteredUserChart } from './registeredUserChart';
import { Link } from '@tanstack/react-router';
import { Separator } from '@mono/ui/separator';

export function OverViewPage() {
  const [days, setDays] = useState('1');
  const { data } = useQuery({
    queryKey: ['getTopTen', days],
    queryFn: () => getTopTen(parseInt(days)),
  });

  function buildSearch(
    teamId: number,
    teamName: string,
    invitationCode: string
  ) {
    return {
      tId: teamId,
      invitationCode: invitationCode,
      tName: teamName,
      from: 'OverViewPage',
    };
  }

  return (
    <div className="h-full flex flex-col gap-4 overflow-hidden">
      <TimeTabs days={days} setDays={setDays} />

      <div className="flex-col flex h-full gap-4 overflow-y-auto">
        <div className="flex gap-4 overflow-hidden min-h-[calc(50vh-60px)] max-h-[calc(50vh-60px)]">
          <div className="flex-1 overflow-hidden">
            <Card className="h-full flex flex-col">
              <CardHeader className="px-7">
                <CardTitle>手动回复top30</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 h-0">
                <ScrollArea className="h-full">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>团队名</TableHead>
                        <TableHead>消息量</TableHead>
                        <TableHead>私信数</TableHead>
                        <TableHead>评论数</TableHead>
                        <TableHead>群消息数</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data?.normal.map((item) => (
                        <TableRow key={item.teamId}>
                          <TableCell className="font-medium">
                            <Link
                              to="/team"
                              search={buildSearch(
                                item.teamId,
                                item.teamName,
                                item.invitationCode
                              )}
                            >
                              {item.teamName}
                            </Link>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.totalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveTotalCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.singleTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveSingleCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.commentTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveCommentCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.groupTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveGroupCount}
                              </span>
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
          <div className="flex-1 overflow-hidden">
            <Card className="h-full flex flex-col">
              <CardHeader className="px-7">
                <CardTitle>自动回复top30</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 h-0">
                <ScrollArea className="h-full">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>团队名</TableHead>
                        <TableHead>消息量</TableHead>
                        <TableHead>私信数</TableHead>
                        <TableHead>评论数</TableHead>
                        <TableHead>群消息数</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data?.auto.map((item) => (
                        <TableRow key={item.teamId}>
                          <TableCell className="font-medium">
                            <Link
                              to="/team"
                              search={buildSearch(
                                item.teamId,
                                item.teamName,
                                item.invitationCode
                              )}
                            >
                              {item.teamName}
                            </Link>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.totalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveTotalCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.singleTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveSingleCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.commentTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveCommentCount}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {item.groupTotalCount}
                              <span className="text-muted-foreground">
                                /{item.receiveGroupCount}
                              </span>
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>

        <Separator />

        <div className="flex overflow-hidden gap-4 min-h-[calc(50vh-60px)] max-h-[calc(50vh-60px)]">
          <DauChart />
          <IncomeChart days={'3'} />
        </div>

        <Separator />

        <div className="flex overflow-hidden gap-4 min-h-[calc(50vh-60px)] max-h-[calc(50vh-60px)]">
          <RegisteredUserChart />
        </div>
      </div>
    </div>
  );
}
