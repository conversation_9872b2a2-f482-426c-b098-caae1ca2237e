import {
  Channel,
  ChannelDetail,
  ChannelFormType,
  ChannelReq,
} from '@/types/channel';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';
// 渠道列表
//   GET /channels
//   接口ID：258125346
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125346
export const channelList = (params: PageQuery & ChannelReq) => {
  return makeRequest<PageData<Channel>>({
    url: `/channels`,
    method: 'GET',
    params,
  });
};

// 创建渠道
//   POST /channels
//   接口ID：258125347
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125347
export const createChannel = (params: ChannelFormType) => {
  return makeRequest<Channel>({
    url: `/channels`,
    method: 'POST',
    data: params,
  });
};

// 更新渠道
//   PATCH /channels/{channelId}
//   接口ID：258125348
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125348
export const updateChannel = (channelId: string, params: ChannelFormType) => {
  return makeRequest<Channel>({
    url: `/channels/${channelId}`,
    method: 'PATCH',
    data: params,
  });
};

// 渠道详情
//   GET /channels/{channelId}
//   接口ID：258125349
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125349
export const channelDetail = (channelId: string) => {
  return makeRequest<ChannelDetail>({
    url: `/channels/${channelId}`,
    method: 'GET',
  });
};

// 渠道删除
//   DELETE /channels/{channelId}
//   接口ID：258125350
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125350
export const deleteChannel = (channelId: string) => {
  return makeRequest({
    url: `/channels/${channelId}`,
    method: 'DELETE',
  });
};

// 更新上下架状态
//   PUT /channels/{channelId}/enabled
//   接口ID：258125351
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-258125351
export const updateChannelStatus = (channelId: string, enabled: boolean) => {
  return makeRequest({
    url: `/channels/${channelId}/enabled`,
    method: 'PUT',
    data: { enabled },
  });
};
