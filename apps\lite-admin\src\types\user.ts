import { PageQuery } from '.';

export type UserAuthorization = {
  accessToken?: string;
  expiresIn?: number;
  refreshToken?: string;
  tokenType?: string;
};

export type UserInfo = {
  id: string;
  username: string;
  avatar: string;
  role: number;
};

export type User = {
  _id: string;
  phone: string;
  nickName: string;
  createdAt: string;
  state: boolean;
  accountCount: number;
  accountCountLimit: number;
  avatar: string;
};

export type UserLoginInput = {
  username: string;
  password: string;
};

export type UserLoginOutput = {
  verifyMfa?: boolean;
  initMfa?: boolean;
  qrcode?: string;
  secret?: string;
  authorization?: string;
  name: string;
  isSuper: boolean;
};

export type UserTwoFactorReq = {
  username: string;
  password: string;
  secret?: string;
  token: string;
};

export type UserPermissionSettingInput = {
  id: string;
  state: boolean;
  accountCountLimit: number;
};

export interface Member {
  id: string;
  /**
   * 头像
   */
  avatar: string;
  /**
   * 渠道码
   */
  channelCode: string;
  /**
   * 注册时间
   */
  createdAt: number;
  /**
   * 归属客服名称
   */
  customerName: string;
  /**
   * 跟进记录总数
   */
  followRecordCount: number;
  /**
   * 最新跟进记录
   */
  newfollowRecord: string;
  /**
   * 昵称
   */
  nickName: string;
  /**
   * 手机号
   */
  phone: string;
  /**
   * 注册来源 0.桌面应用 1.网站 2.移动应用 3.管理员手动注册 4.API注册 5.其他
   */
  registrationSource: number;
  /**
   * 团队数
   */
  teamCount: number;
  /**
   * 最后使用时间
   */
  updatedAt: number;
}

export type MemberReq = {
  /**
   * 渠道码
   */
  channelCode?: string;
  /**
   * 注册结束时间
   */
  createEndTime?: number;
  /**
   * 注册开始时间
   */
  createStartTime?: number;
  /**
   * 归属人ID
   */
  customerId?: string;
  /**
   * 最后使用结束时间
   */
  lastUseEndTime?: number;
  /**
   * 最后使用开始时间
   */
  lastUseStartTime?: number;
  /**
   * 手机号
   */
  phone?: string;
} & PageQuery;

export type Device = {
  id: string;
  userId: string;
  version: string;
  deviceId: string;
  loginTime: number;
  isActive: boolean;
};
