import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { omitBy } from 'lodash';
import { CalendarRange } from '@mono/ui/common/CalendarRange';
import { AnalysisReq } from '@/types/analysis';
import { reduceDuration } from '@mono/utils/day';

type SearchForm = {
  dateRange?: {
    from: Date;
    to: Date;
  };
} & Omit<AnalysisReq, 'startTime' | 'endTime'>;

export function TableSearch({
  onSearch,
}: {
  values?: AnalysisReq;
  onSearch: (values: AnalysisReq) => void;
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      dateRange: undefined,
    },
  });

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values);
    const params: AnalysisReq = {
      startTime: values?.dateRange?.from?.getTime(),
      endTime: values.dateRange?.to?.getTime(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="dateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">选择时间段</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
                disabled={(date) => date < new Date(reduceDuration(2, 'M'))}
              />
            </FormItem>
          )}
        />
        {/* <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>客服</FormLabel>
              <FormControl>
                <SelectService
                  value={field.value ?? ''}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        /> */}
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
