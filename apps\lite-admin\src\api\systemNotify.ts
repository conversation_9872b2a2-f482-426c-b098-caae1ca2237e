import { makeRequest } from './index';
import { PageCountQuery, PageResult } from '@/types/index';
import {
  SystemNotifyDto,
  AddSystemNotifyConfigInput,
  SystemNotifyListInput,
} from '@/types/systemNotify.ts';

/**
 * 获取系统通知列表
 */
export const systemNotifyList = (
  params: PageCountQuery & SystemNotifyListInput
) => {
  if (params) {
    if (!params.name) {
      delete params.name;
    }
  }
  return makeRequest<PageResult<SystemNotifyDto>>({
    url: '/message',
    method: 'GET',
    params,
  });
};

/**
 * 新增系统通知
 */
export const addSystemNotiryConfig = async (
  input: AddSystemNotifyConfigInput
) => {
  console.log('新增系统通知', input);
  return makeRequest<object>({
    url: `/message`,
    method: 'Post',
    data: input,
  });
};

/**
 * 设置系统通知
 */
export const setSystemNotifyConfig = async (
  input: AddSystemNotifyConfigInput
) => {
  console.log('设置系统通知', input);
  return makeRequest<object>({
    url: `/message/${input.id}`,
    method: 'Patch',
    data: input,
  });
};

/**
 * 系统通知详情
 * @param id
 * @returns
 */
export const getSystemNotifyDetail = async (id: string) => {
  return makeRequest<object>({
    url: `/message/${id}`,
    method: 'Get',
  });
};

/**
 * 删除系统通知
 */
export const deleteSystemNotify = (id: string) => {
  return makeRequest<object>({
    url: `/message/${id}`,
    method: 'Delete',
  });
};
