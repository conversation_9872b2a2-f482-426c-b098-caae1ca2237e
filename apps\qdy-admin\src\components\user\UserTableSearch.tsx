import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { CalendarRange } from '../CalendarRange';
import { omitBy } from 'lodash';
import { UserParams } from '@/types/user';
import { SelectChannel } from './SelectChannel';

const formSchema = z.object({
  phone: z.string().optional(),
  registerTimeRange: z
    .object({
      from: z.date(),
      to: z.date(),
    })
    .optional(),
  channelId: z.string().optional(),
});

export function UserTableSearch({
  values,
  onSearch,
}: {
  values: UserParams;
  onSearch: (values: UserParams) => void;
}) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: values,
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const params: UserParams = {
      phone: values.phone?.trim(),
      startTime: values.registerTimeRange?.from?.getTime(),
      endTime: values.registerTimeRange?.to?.getTime(),
      channelId: values.channelId?.trim(),
    };
    const paramsOmitEmpty = omitBy(params, (value) => !value);
    onSearch(paramsOmitEmpty);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-4 justify-start p-0.5"
      >
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="registerTimeRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange
                value={field.value}
                onChange={field.onChange}
              ></CalendarRange>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="channelId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>渠道</FormLabel>
              <FormControl>
                <SelectChannel
                  value={`${field.value}`}
                  onChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
