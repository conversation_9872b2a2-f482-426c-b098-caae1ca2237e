import TableData from '@/components/table';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useRef, useState } from 'react';
import { orderList } from '@/api/order';
import { OrderParams } from '@/types/order';
import { getTableColumns } from './columns';
import { TableSearch } from './TableSearch';
import { useParams } from '@tanstack/react-router';
import { useApplicationDetail } from '@/hooks/useApplicationDetail';
import { Button } from '@mono/ui/button';
import { Plus } from 'lucide-react';
import { CreateOrder } from './CreateOrder';

export function ApplicationOrder() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const [showCreateOrderModal, setShowCreateOrderModal] = useState(false);
  const searchParams = useRef<OrderParams>({});
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  // 获取应用详情以判断应用类型
  const { data: applicationDetail } = useApplicationDetail();

  const query = useQuery({
    queryKey: ['orderList', pagination],
    queryFn: () =>
      orderList(applicationId as string, {
        ...searchParams?.current,
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
    placeholderData: keepPreviousData,
  });
  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  // 判断是否为渠道商应用
  const isChannelPartner =
    applicationDetail?.applicationType === 'CHANNEL_PARTNER' &&
    applicationDetail?.status === 1;

  return (
    <div className="w-full h-full flex flex-col gap-3 px-[20px] py-5 overflow-hidden">
      {/* 工具栏 */}
      <div className="flex justify-between items-center">
        <TableSearch
          values={searchParams.current}
          onSearch={(values: OrderParams) => {
            searchParams.current = values;
            setPagination({
              pageIndex: 0,
              pageSize: 20,
            });
            query.refetch();
          }}
        />

        {isChannelPartner && (
          <Button
            onClick={() => setShowCreateOrderModal(true)}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            创建订单
          </Button>
        )}
      </div>

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />

      {/* 创建订单弹窗 */}
      {isChannelPartner && (
        <CreateOrder
          open={showCreateOrderModal}
          onClose={() => setShowCreateOrderModal(false)}
        />
      )}
    </div>
  );
}
