import TableData from '@/components/table';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { PaginationState } from '@tanstack/react-table';
import { useMemo, useRef, useState } from 'react';
import { orderList } from '@/api/order';
import { OrderParams } from '@/types/order';
import { getTableColumns } from './columns';
import { TableSearch } from './TableSearch';
import { useParams } from '@tanstack/react-router';

export function ApplicationOrder() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  });
  const searchParams = useRef<OrderParams>({});
  const params = useParams({ strict: false });
  const applicationId = params.applicationId;

  const query = useQuery({
    queryKey: ['orderList', pagination],
    queryFn: () =>
      orderList(applicationId as string, {
        ...searchParams?.current,
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      }),
    placeholderData: keepPreviousData,
  });
  const columns = useMemo(() => {
    return getTableColumns();
  }, []);

  return (
    <div className="w-full h-full flex flex-col gap-3 px-[20px] py-5 overflow-hidden">
      <TableSearch
        values={searchParams.current}
        onSearch={(values: OrderParams) => {
          searchParams.current = values;
          setPagination({
            pageIndex: 0,
            pageSize: 20,
          });
          query.refetch();
        }}
      />

      <TableData
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
