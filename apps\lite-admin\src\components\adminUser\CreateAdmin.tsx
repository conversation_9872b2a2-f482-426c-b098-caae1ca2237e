import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { LoadingButton } from '@/components/loading-button.tsx';
import { z } from 'zod';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';

import { createAdmin, getAdminById, updateAdmin } from '@/api/admin';
import { useEffect } from 'react';
import { PasswordInput } from '@/components/PasswordInput.tsx';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Switch } from '@mono/ui/switch';
import { fileToBase64 } from '@mono/utils/file';
import { SelectFile } from '@mono/ui/common/SelectFile';

interface CreateAdminProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSuccess: () => void;
}

interface EditorAdminProps extends CreateAdminProps {
  itemId: string;
}

const formSchema = z.object({
  name: z.string().min(1, '姓名不能为空'),
  username: z.string().min(1, '账号不能为空'),
  role: z.number().int(),
  status: z.number().int(),
  qrCode: z.object({
    file: z.instanceof(File).optional(),
    url: z.string().optional(),
  }),
});
const addFormSchema = formSchema.extend({
  password: z.string().min(1, '密码不能为空'),
});

const editFormSchema = formSchema.extend({
  password: z.string().optional(),
});

// type BaseFormData = z.infer<typeof formSchema>;
type AddFormData = z.infer<typeof addFormSchema>;
type EditFormData = z.infer<typeof editFormSchema>;

export function EditorAdmin({
  itemId,
  open,
  setOpen,
  onSuccess,
}: EditorAdminProps) {
  const form = useForm<AddFormData | EditFormData>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      name: '',
      username: '',
      role: 1,
      status: 1,
      qrCode: {},
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof editFormSchema>) => {
      const { qrCode, ...rest } = values;
      let base64 = '';
      // qrCode转换为base64
      if (qrCode.file) {
        base64 = await fileToBase64(qrCode.file);
      }
      return updateAdmin(itemId, { ...rest, qrCode: base64.split(',')[1] });
    },
    onSuccess: () => {
      setOpen(false);
      onSuccess();
    },
  });

  const onSubmit = (values: z.infer<typeof editFormSchema>) => {
    mutation.mutate(values);
  };

  const query = useQuery({
    queryKey: ['getAdminById', itemId],
    queryFn: () => getAdminById(itemId),
    enabled: open,
  });

  useEffect(() => {
    if (query.data) {
      const res = query.data;
      form.reset({
        name: res.name,
        username: res.username,
        role: res.role,
        status: res.status,
        qrCode: { url: res.qrCode },
      });
    }
    return () => {
      form.reset();
    };
  }, [form.reset, query.data]);

  return (
    <CreateOrEditorAdminTemplate
      title="编辑账号"
      open={open}
      setOpen={setOpen}
      isLoading={mutation.isPending}
      onSubmit={onSubmit}
      form={form}
      type="edit"
    />
  );
}

export function CreateAdmin({ open, setOpen, onSuccess }: CreateAdminProps) {
  const form = useForm<AddFormData | EditFormData>({
    resolver: zodResolver(addFormSchema),
    defaultValues: {
      name: '',
      username: '',
      password: '',
      role: 1,
      status: 1,
      qrCode: {},
    },
    mode: 'onChange',
  });

  const mutation = useMutation({
    mutationFn: createAdmin,
    onSuccess: () => {
      setOpen(false);
      onSuccess();
    },
  });

  const onSubmit = async (values: AddFormData | EditFormData) => {
    const { qrCode, ...rest } = values;
    let base64 = '';
    // qrCode转换为base64
    if (qrCode.file) {
      base64 = await fileToBase64(qrCode.file);
    }
    mutation.mutate({ ...rest, qrCode: base64.split(',')[1] });
  };

  useEffect(() => {
    // 当open变为false时，重置表单
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  return (
    <CreateOrEditorAdminTemplate
      title="新增账号"
      open={open}
      setOpen={setOpen}
      isLoading={mutation.isPending}
      onSubmit={onSubmit}
      form={form}
      type="create"
    />
  );
}

interface CreateOrEditorAdminProps {
  title: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  isLoading: boolean;
  onSubmit: (values: AddFormData | EditFormData) => void;
  form: UseFormReturn<AddFormData | EditFormData>;
  type: 'create' | 'edit';
}

function CreateOrEditorAdminTemplate({
  title,
  open,
  setOpen,
  isLoading,
  onSubmit,
  form,
  type,
}: CreateOrEditorAdminProps) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{title}</DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>
            <div className="flex flex-col my-6 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>姓名</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className="flex-grow"
                        autoComplete="off"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className="flex-grow"
                        autoComplete="off"
                        disabled={type === 'edit'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {type !== 'edit' && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>密码</FormLabel>
                      <FormControl>
                        <PasswordInput
                          {...field}
                          autoComplete="current-password"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          field.onChange(parseInt(value, 10));
                        }}
                        value={`${field.value}`}
                        className="flex space-x-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="1" />
                          </FormControl>
                          <FormLabel className="font-normal">管理员</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="2" />
                          </FormControl>
                          <FormLabel className="font-normal">客服</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
              {form.getValues('role') === 2 && (
                <>
                  <FormField
                    control={form.control}
                    name="qrCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>二维码</FormLabel>
                        <FormControl>
                          <SelectFile
                            url={field.value.url}
                            onchange={(file) => {
                              field.onChange({ file, url: '' });
                            }}
                            accept="image/*"
                            className="w-28 h-28"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormLabel>状态</FormLabel>
                        <FormControl>
                          <Switch
                            checked={field.value === 1}
                            onCheckedChange={(checked) => {
                              field.onChange(checked ? 1 : 0);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </>
              )}
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={isLoading}
                disabled={isLoading || !form.formState.isValid}
                type="submit"
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
