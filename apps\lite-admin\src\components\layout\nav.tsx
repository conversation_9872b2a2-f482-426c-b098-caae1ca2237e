import { Package2 } from 'lucide-react';
import { Link } from '@tanstack/react-router';
import { navs } from './navs-datasource';
import { Mine } from './mine';
import { ScrollArea } from '@mono/ui/scroll-area';
// import { useUserStore } from '@/store/user.ts';
// function RouterSpinner() {
//   const isLoading = useRouterState({ select: (s) => s.status === 'pending' });
//   return <Spinner show={isLoading} />;
// }

export function Nav() {
  // const isSuper = useUserStore((state) => state.isSuper);

  return (
    <aside className="flex flex-col h-full overflow-hidden border-r bg-muted/40">
      <div className="flex flex-col flex-1 h-0 gap-2">
        <div className="flex flex-shrink-0 h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link className="flex items-center gap-2 font-semibold" to={'/'}>
            <Package2 className="w-6 h-6" />
            <span className="truncate max-w-36">后台管理系统</span>
          </Link>
        </div>
        <ScrollArea className="flex-1 h-0">
          <nav className="grid items-start px-4 space-y-1 text-sm font-medium">
            {navs.map((item) => (
              <Link
                key={item.path}
                activeProps={{
                  className: 'bg-muted text-primary',
                }}
                className="flex items-center gap-3 px-3 py-3 transition-all rounded-lg text-muted-foreground hover:text-primary"
                to={item.path}
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}
          </nav>
        </ScrollArea>

        {/* <RouterSpinner /> */}

        <div className="flex-shrink-0 p-4">
          <Mine />
        </div>
      </div>
    </aside>
  );
}
