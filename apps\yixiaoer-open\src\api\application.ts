import {
  Application,
  ApplicationDTO,
  ApplicationOverview,
  ApplicationParams,
} from '@/types/application';
import { makeRequest } from '.';
import { PageData, PageQuery } from '@/types';

// 创建应用
// POST /open-platform/applications
// 接口ID：306139751
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139751
export const createApplication = (params: ApplicationDTO) => {
  return makeRequest({
    url: '/open-platform/applications',
    method: 'POST',
    data: params,
  });
};

// 更新应用
// PUT /open-platform/applications/{id}
// 接口ID：306139754
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139754
export const updateApplication = (params: ApplicationDTO) => {
  return makeRequest({
    url: `/open-platform/applications/${params.id}`,
    method: 'PUT',
    data: params,
  });
};

// 删除应用
// DELETE /open-platform/applications/{id}
// 接口ID：306139755
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139755
export const deleteApplication = (id: string) => {
  return makeRequest({
    url: `/open-platform/applications/${id}`,
    method: 'DELETE',
  });
};

// 重新生成应用密钥
// POST /open-platform/applications/{id}/regenerate-secret
// 接口ID：306139756
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139756
export const regenerateSecret = (id: string) => {
  return makeRequest({
    url: `/open-platform/applications/${id}/regenerate-secret`,
    method: 'POST',
  });
};

// 获取应用列表
// GET /open-platform/applications
// 接口ID：306139752
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139752
export const applicationList = (params: PageQuery & ApplicationParams) => {
  return makeRequest<PageData<Application>>({
    url: '/open-platform/applications',
    method: 'GET',
    params,
  });
};

// 获取应用详情
// GET /open-platform/applications/{id}
// 接口ID：306139753
// 接口地址：https://app.apifox.com/link/project/5710261/apis/api-306139753
export const applicationDetail = (id: string) => {
  return makeRequest<Application>({
    url: `/open-platform/applications/${id}`,
    method: 'GET',
  });
};

// 获取应用概览
// GET /open-platform/applications/{id}/overview
// 接口ID：306139753
export const applicationOverview = (id: string) => {
  return makeRequest<ApplicationOverview>({
    url: `/open-platform/applications/${id}/overview`,
    method: 'GET',
  });
};
