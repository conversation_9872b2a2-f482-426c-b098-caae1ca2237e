export interface Application {
  id: string;
  name: string;
  appId: string;
  secretKey: string;
  description: string;
  status: number;
  userId: string;
  createdAt: number;
  updatedAt: number;
  userRole: string;
  canManage: boolean;
  userIdentity: string;
  userIdentityLabel: string;
}

export type ApplicationDTO = {
  id?: string;
  name: string;
  description: string;
};

export type ApplicationParams = {
  keyword?: string;
  status?: number;
};
