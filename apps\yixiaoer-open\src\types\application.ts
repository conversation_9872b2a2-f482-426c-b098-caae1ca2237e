export interface Application {
  id: string;
  name: string;
  appId: string;
  secretKey: string;
  description: string;
  status: 0 | 1 | 2;
  applicationType: 'TECHNICAL_SERVICE' | 'CHANNEL_PARTNER';
  availableBalance: number;
  totalAccountPoints: number;
  usedAccountPoints: number;
  totalTraffic: number;
  usedTraffic: number;
  userId: string;
  createdAt: number;
  updatedAt: number;
  userRole: string;
  canManage: boolean;
  userIdentity: string;
  userIdentityLabel: string;
}

export type ApplicationDTO = {
  id?: string;
  name: string;
  applicationType: 'TECHNICAL_SERVICE' | 'CHANNEL_PARTNER';
};

export type ApplicationParams = {
  keyword?: string;
  status?: number;
};

export const applicationTypeMap = {
  TECHNICAL_SERVICE: 'Apass',
  CHANNEL_PARTNER: '渠道商',
} as const;

export interface ApplicationOverview {
  availableBalance: number;
  totalAccountPoints: number;
  usedAccountPoints: number;
  totalTraffic: number;
  usedTraffic: number;
  totalUsers: number;
  totalTeams: number;
  dailyStats: {
    date: string;
    newTraffic: number;
    usedTraffic: number;
    newAccountPoints: number;
    usedAccountPoints: number;
  }[];
}

// 应用状态映射
export const appStatusMap = {
  0: '待审',
  1: '正常',
  2: '拒绝',
} as const;

// 应用状态样式映射
export const appStatusStyleMap = {
  0: 'text-black-600',
  1: 'text-green-600',
  2: 'text-red-600',
} as const;
