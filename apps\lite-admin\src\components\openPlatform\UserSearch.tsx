import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { DatePickerWithRange } from '@/components/DatePickerWithRange';
import { OpenPlatformUserParams } from '@/types/openPlatform';
import { Search, RotateCcw } from 'lucide-react';

interface UserSearchProps {
  onSearch: (params: OpenPlatformUserParams) => void;
  values: Partial<OpenPlatformUserParams>;
}

type SearchForm = {
  phone?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
};

export function UserSearch({ onSearch, values }: UserSearchProps) {
  const form = useForm<SearchForm>({
    defaultValues: {
      phone: values.phone || '',
      dateRange: values.registeredStartTime && values.registeredEndTime ? {
        from: new Date(values.registeredStartTime),
        to: new Date(values.registeredEndTime),
      } : undefined,
    },
  });

  const handleSubmit = (data: SearchForm) => {
    const params: OpenPlatformUserParams = {
      phone: data.phone || undefined,
      registeredStartTime: data.dateRange?.from?.getTime(),
      registeredEndTime: data.dateRange?.to?.getTime(),
    };
    onSearch(params);
  };

  const handleReset = () => {
    form.reset({
      phone: '',
      dateRange: undefined,
    });
    onSearch({});
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="flex flex-wrap gap-4 items-end">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="flex-1 min-w-[200px]">
                <FormLabel>手机号码</FormLabel>
                <Input
                  {...field}
                  placeholder="请输入手机号码"
                  className="w-full"
                />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="dateRange"
            render={({ field }) => (
              <FormItem className="flex-1 min-w-[250px]">
                <FormLabel>注册时间</FormLabel>
                <DatePickerWithRange
                  onChange={(dateRange) => {
                    field.onChange(dateRange?.from && dateRange?.to ? {
                      from: dateRange.from,
                      to: dateRange.to,
                    } : undefined);
                  }}
                />
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button type="submit" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              搜索
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              className="flex items-center gap-2"
            >
              <RotateCcw className="w-4 h-4" />
              重置
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
