import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { LoadingButton } from '@/components/loading-button';
import { useEffect, useMemo } from 'react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { QuestionDto } from '@/types/question';
import { addQuestionConfig, setQuestionConfig } from '@/api/question';

export function QuestionSetting({
  question,
  open,
  setOpen,
}: {
  question?: QuestionDto;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const queryClient = useQueryClient();
  const formSchema = useMemo(() => {
    return z.object({
      id: z.string().optional(),
      title: z.string().min(1),
      sort: z.number().min(1),
      questionUrl: z.string().optional(),
    });
  }, [question]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      title: '',
      sort: 1,
      questionUrl: '',
    },
  });

  useEffect(() => {
    if (open && question) {
      form.setValue('id', question.id);
      form.setValue('title', question.title);
      form.setValue('sort', question.sort);
      form.setValue('questionUrl', question.questionUrl);
    }
  }, [form, question, open]);

  useEffect(() => {
    if (!open) form.reset();
  }, [open]);

  const mutation = useMutation({
    mutationFn: addQuestionConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['questionList'],
      });
    },
  });

  const editMutation = useMutation({
    mutationFn: setQuestionConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['questionList'],
      });
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (values.id) {
      editMutation.mutate({
        id: values.id,
        title: values.title,
        sort: values.sort,
        questionUrl: values.questionUrl,
      });
    } else {
      mutation.mutate({
        title: values.title,
        sort: values.sort,
        questionUrl: values.questionUrl,
      });
    }
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{question?.id ? '修改' : '添加'}广告位</DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      名称:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="flex-grow"
                        placeholder="填写名称"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="questionUrl"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      跳转地址:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="flex-grow"
                        placeholder="填写名称"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="sort"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      排序：
                    </FormLabel>
                    <FormControl>
                      <div>
                        <Input
                          type="number"
                          placeholder="请输入"
                          min={1}
                          {...field}
                          onChange={(e) => {
                            const value =
                              e.target.value ?
                                parseInt(e.target.value, 10)
                              : '';
                            field.onChange(value);
                          }}
                        />
                        <FormMessage />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
