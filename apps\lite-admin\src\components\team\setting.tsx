import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { LoadingButton } from '@/components/loading-button';
import { Team } from '@/types/team';
import { setTeamConfig } from '@/api/team';
import { useEffect, useState } from 'react';
import { convertObjectToNumber } from '@/lib/type';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { DatePicker } from '@/components/DatePicker.tsx';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';

const formSchema = z.object({
  accountCountLimit: z.string().min(1),
  memberCountLimit: z.string().min(1),
  expiredAt: z.number().optional(),
  capacityType: z.string(),
});

export function TeamSetting({
  team,
  open,
  setOpen,
}: {
  team: Team;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const [enabled, setEnabled] = useState<boolean>(false);

  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      accountCountLimit: '0',
      memberCountLimit: '0',
      capacityType: '',
      expiredAt: undefined,
    },
  });

  useEffect(() => {
    if (open && team) {
      console.log(team);
      setEnabled(team.enabled);
      form.setValue('accountCountLimit', team.accountCountLimit.toString());
      form.setValue('memberCountLimit', team.memberCountLimit.toString());
      form.setValue('capacityType', team.capacityType);
      form.setValue('expiredAt', new Date(team.expiredAt).getTime());
    }
  }, [form, team, open]);

  const mutation = useMutation({
    mutationFn: setTeamConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['teamList'],
      });
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    const { capacityType, ...rest } = values;
    const data = convertObjectToNumber(rest);
    const teamId = team.id;
    mutation.mutate({
      id: teamId,
      enabled: enabled,
      accountCountLimit: data.accountCountLimit,
      memberCountLimit: data.memberCountLimit,
      capacity: capacityType,
      expiredAt: data.expiredAt === 0 ? undefined : data.expiredAt,
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>vip设置</DialogTitle>

              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="accountCountLimit"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-20 flex-shrink-0 text-right mr-2">
                      账号数:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        className="flex-grow"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="memberCountLimit"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-20 flex-shrink-0 text-right mr-2">
                      成员数:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        className="flex-grow"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="capacityType"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 space-y-0">
                    <FormLabel className="w-20 flex-shrink-0 text-right mr-2">
                      素材库额度:
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="flex flex-row space-x-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="M_500" />
                          </FormControl>
                          <FormLabel className="font-normal">500M</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="G_1" />
                          </FormControl>
                          <FormLabel className="font-normal">1G</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="G_5" />
                          </FormControl>
                          <FormLabel className="font-normal">5G</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="G_20" />
                          </FormControl>
                          <FormLabel className="font-normal">20G</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="G_50" />
                          </FormControl>
                          <FormLabel className="font-normal">50G</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiredAt"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 space-y-0">
                    <FormLabel className="w-20 flex-shrink-0 text-right mr-2">
                      到期日期:
                    </FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        className={'w-full'}
                        onChange={(date) => {
                          form.setValue('expiredAt', date?.getTime());
                          field.onChange(date?.getTime());
                          form.trigger('expiredAt').then();
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
