type Type = 'invitation' | 'proposal' | 'regular' | 'system';

export type SystemNotifyDto = {
  /**
   * 消息Id
   */
  id: string;
  /**
   * 标题
   */
  title: string;
  /**
   * 内容
   */
  content: string;
  /**
   * 是否弹窗
   */
  isPopUp: boolean;
  /**
   * 消息类型 system系统
   */
  type: Type;
  /**
   * 创建时间
   */
  createdAt: number;
};

export class SystemNotifyListInput {
  name?: string;
}

export type AddSystemNotifyConfigInput = {
  id?: string;
  title: string;
  content: string;
  isPopUp: boolean;
};
