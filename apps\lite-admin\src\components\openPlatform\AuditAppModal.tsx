import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@mono/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { auditApplication } from '@/api/openPlatform';
import { OpenPlatformApp } from '@/types/openPlatform';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  status: z.enum(['1', '2'], {
    required_error: '请选择审核结果',
  }),
});

type FormData = z.infer<typeof formSchema>;

interface AuditAppModalProps {
  open: boolean;
  onClose: () => void;
  app: OpenPlatformApp | null;
}

export function AuditAppModal({
  open,
  onClose,
  app,
}: AuditAppModalProps) {
  const queryClient = useQueryClient();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: '1', // 默认选择通过
    },
  });

  const mutation = useMutation({
    mutationFn: auditApplication,
    onSuccess: () => {
      toast.success('应用审核成功');
      queryClient.invalidateQueries({ queryKey: ['userApps'] });
      queryClient.invalidateQueries({ queryKey: ['openPlatformUsers'] });
      handleClose();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : '应用审核失败';
      console.log(errorMessage);
      // toast.error(errorMessage);
    },
  });

  const onSubmit = (data: FormData) => {
    if (!app) return;

    const status = parseInt(data.status);
    
    // 确认操作
    const action = status === 1 ? '通过' : '拒绝';
    if (confirm(`确定要${action}应用"${app.name}"的审核吗？`)) {
      mutation.mutate({
        id: app.id,
        status,
      });
    }
  };

  const handleClose = () => {
    if (!mutation.isPending) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>应用审核</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 应用信息 */}
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">应用信息</div>
              <div className="p-4 bg-muted/50 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">应用名称：</span>
                  <span className="text-sm">{app?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">应用ID：</span>
                  <span className="text-sm font-mono">{app?.appId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">应用类型：</span>
                  <span className="text-sm">
                    {app?.applicationType === 'TECHNICAL_SERVICE' ? '技术服务商' : '渠道商'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium">创建时间：</span>
                  <span className="text-sm">
                    {app?.createdAt ? 
                      new Date(app.createdAt).toLocaleString('zh-CN') 
                      : '-'
                    }
                  </span>
                </div>
              </div>
            </div>

            {/* 审核选项 */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-base font-medium">审核结果</FormLabel>
                  <FormControl>
                    <RadioGroup
                      value={field.value}
                      onValueChange={field.onChange}
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="approve" />
                        <Label htmlFor="approve" className="text-sm font-normal">
                          通过 - 应用审核通过，可以正常使用
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="reject" />
                        <Label htmlFor="reject" className="text-sm font-normal">
                          拒绝 - 应用审核不通过，需要重新提交
                        </Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={mutation.isPending}
              >
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                保存
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
