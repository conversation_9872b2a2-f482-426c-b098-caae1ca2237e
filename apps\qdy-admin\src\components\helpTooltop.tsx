import SauryTooltip from '@/components/tooltip';
import { cn } from '@/lib/utils';
import { CircleHelp } from 'lucide-react';
import React from 'react';
export function HelpTooltop({
  title,
  className,
  ...props
}: { title: React.ReactNode } & React.ComponentProps<typeof CircleHelp>) {
  return (
    <SauryTooltip tooltip={title}>
      <CircleHelp
        {...props}
        className={cn('w-4 h-4 text-muted-foreground', className)}
      />
    </SauryTooltip>
  );
}
