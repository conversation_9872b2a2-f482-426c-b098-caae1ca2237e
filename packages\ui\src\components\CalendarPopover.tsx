import { Button } from '@mono/ui/button';
import { FormControl } from '@mono/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { cn } from '@mono/utils';
import { CalendarIcon } from 'lucide-react';
import { formatYearMonthDay } from '@mono/utils/day';
import { Calendar } from '@mono/ui/calendar';

export function CalendarPopover({
  value,
  onChange,
}: {
  value?: Date;
  onChange?: (value?: Date) => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant={'outline'}
            className={cn(
              'w-full text-left font-normal',
              !value && 'text-muted-foreground'
            )}
          >
            {value ? formatYearMonthDay(value) : <span>选择时间段</span>}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={value}
          onSelect={onChange}
          disabled={(date) =>
            date < new Date() || date < new Date('1900-01-01')
          }
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
