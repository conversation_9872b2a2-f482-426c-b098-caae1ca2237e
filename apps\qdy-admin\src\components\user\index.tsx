import { useMemo, useRef, useState } from 'react';
import { UserTable } from '@/components/user/UserTable';
import { getUserColumns } from '@/components/user/columns';
import { User } from '@/types/user';
import { ColumnDef } from '@tanstack/react-table';
import { SetChannel } from './setChannel';
import { DeleteUser } from './deleteUser';

export function UserPage() {
  const [open, setOpen] = useState(false);
  const [selectUser, setSelectUser] = useState<User | undefined>();
  const [openDeleteUser, setOpenDeleteUser] = useState(false);
  const userId = useRef(-1);

  const columns = useMemo(
    () =>
      getUserColumns(
        (user: User) => {
          setSelectUser(user);
          setOpen(true);
        },
        (id: number) => {
          userId.current = id;
          setOpenDeleteUser(true);
        }
      ),
    []
  );

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <UserTable columns={columns as ColumnDef<User, unknown>[]} />
      <SetChannel open={open} setOpen={setOpen} user={selectUser} />
      <DeleteUser
        open={openDeleteUser}
        setOpen={setOpenDeleteUser}
        userId={userId.current}
      />
    </div>
  );
}
