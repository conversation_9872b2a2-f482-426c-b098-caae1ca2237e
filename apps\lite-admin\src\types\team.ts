export interface Team {
  id: string;
  name: string;
  phone: string;
  accountCountLimit: number;
  accountCount: number;
  memberCountLimit: number;
  capacityType: string;
  usedCapacity: number;
  capacity: number;
  memberCount: number;
  createdAt: string;
  expiredAt: string;
  enabled: boolean;
  code: string;
  isVip: boolean;
  interestCount: number;
  customerName: string;
  salesType: number;
}

export type TeamsReq = {
  /**
   * 归属人Id
   */
  customerId?: string;
  /**
   * 创建时间结束时间 <默认 0>
   */
  endTime?: number;
  /**
   * 到期时间结束时间
   */
  expiredEndTime?: number;
  /**
   * 到期时间开始时间
   */
  expiredStartTime?: number;
  /**
   * 是否有效团队 true有效 false无效
   */
  isEffective?: boolean;
  /**
   * vip开通 true已开通 false未开通
   */
  isVip?: boolean;
  /**
   * 手机号查询
   */
  phone?: string;
  /**
   * 创建时间开始时间 <默认 0>
   */
  startTime?: number;
  /**
   * 团队名称查询｜团队编号
   */
  teamName?: string;
  salesType?: number;
};

export type TeamConfigInput = {
  id: string;
  enabled: boolean;
  memberCountLimit: number;
  accountCountLimit: number;
  capacity: string;
  expiredAt: undefined | number;
};
