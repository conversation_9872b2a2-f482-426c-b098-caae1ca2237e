import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@mono/ui/dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useMemo, useState } from 'react';
import { DataTable } from '@/components/dataTable.tsx';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { Device } from '@/types/user.ts';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getUserDevices, sendLogs } from '@/api/user.ts';
import { Button } from '@mono/ui/button';
import { format } from 'date-fns';

interface DeviceRecordsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  Id: string;
}
export function DeviceRecords({ open, onOpenChange, Id }: DeviceRecordsProps) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const columns = useMemo<ColumnDef<Device>[]>(() => {
    return [
      {
        header: '客户端版本',
        accessorKey: 'version',
        cell: ({ row }) => {
          const { version } = row.original;
          return <div className="flex flex-col gap-1 w-36">{version}</div>;
        },
      },
      {
        header: '设备版本',
        accessorKey: 'deviceId',
        cell: ({ row }) => {
          const { deviceId } = row.original;
          return <div className="flex flex-col gap-1 w-56">{deviceId}</div>;
        },
      },
      {
        header: '最后登录时间',
        accessorKey: 'loginTime',
        cell: ({ row }) => {
          const { loginTime } = row.original;
          return (
            <div className="flex flex-col gap-1 w-36">
              {format(loginTime, 'yyyy-MM-dd HH:mm:ss')}
            </div>
          );
        },
      },
      {
        header: '设备状态',
        accessorKey: 'isActive',
        cell: ({ row }) => {
          const { isActive } = row.original;
          return (
            <div className="flex flex-col gap-1 w-36">
              {isActive ? '在线' : '离线'}
            </div>
          );
        },
      },
      {
        header: '操作',
        accessorKey: 'operation',
        cell: ({ row }) => {
          const { id } = row.original;
          return (
            <>
              <Button onClick={() => mutation.mutate(id)}>获取日志</Button>
            </>
          );
        },
      },
    ];
  }, []);

  const query = useQuery({
    queryKey: ['deviceList', pagination, Id],
    queryFn: () => getUserDevices(Id),
  });

  const mutation = useMutation({
    mutationFn: (deviceId: string) => sendLogs(deviceId),
    mutationKey: ['sendLogs'],
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-svh overflow-hidden flex flex-col gap-5 !min-w-[400px] !max-w-[1000px]">
        <DialogHeader>
          <DialogTitle>查看设备</DialogTitle>
          <VisuallyHidden>
            <DialogDescription />
          </VisuallyHidden>
        </DialogHeader>
        <div className="flex-1 overflow-x-hidden overflow-y-auto">
          <DataTable
            columns={columns}
            data={query.data as unknown as Device[]}
            rowCount={0}
            pagination={pagination}
            setPagination={setPagination}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
