import { Create } from '@/components/channel/create';
// import { TableBase } from '@/components/channel/table';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo, useRef, useState } from 'react';
import { getColumns } from '@/components/channel/columns';
import { alertBaseManager } from '@/components/alertBase';
import { Channel, ChannelParams } from '@/types/channel';
import { deleteChannel, updateChannelStatus } from '@/api/channel';
import { ColumnDef } from '@tanstack/react-table';
import { ChannelTableSearch } from './ChannelTableSearch';
import { ResetPassword } from './resetPassword';
import TableData from '@/components/table';
import { usePageQuery } from '@/hooks/usePageQuery';
import { getChannels } from '@/api/channel';

export function ChannelPage() {
  const queryClient = useQueryClient();
  const [currentChannel, setCurrentChannel] = useState<Channel>();
  const [searchParams, setSearchParams] = useState<ChannelParams>();
  const resetPasswordChannel = useRef<Channel>();
  const [openResetPassword, setOpenResetPassword] = useState(false);

  const deleteMutation = useMutation({
    mutationFn: deleteChannel,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['getChannels'] });
    },
  });
  const updateChannelStatusMutation = useMutation({
    mutationFn: updateChannelStatus,
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['getChannels'] });
    },
  });
  const columns = useMemo(() => {
    return getColumns((item, type, checked) => {
      if (type === 'update') {
        setCurrentChannel({ ...item });
      } else if (type === 'del') {
        alertBaseManager.open({
          title: '删除渠道',
          description: '确定删除该渠道吗？',
          onSubmit: () => {
            deleteMutation.mutate(item.id);
          },
        });
      } else if (type === 'status') {
        updateChannelStatusMutation.mutate({
          channelId: item.id,
          status: checked!,
        });
      } else if (type === 'resetPassword') {
        resetPasswordChannel.current = { ...item };
        setOpenResetPassword(true);
      }
    });
  }, [deleteMutation, updateChannelStatusMutation]);

  const { query, pagination, setPagination } = usePageQuery(
    ['getChannels'],
    getChannels,
    searchParams
  );

  return (
    <div className="flex flex-col w-full h-full overflow-hidden gap-4">
      <div className="flex flex-shrink-0 gap-2">
        <Create
          channel={currentChannel}
          reset={() => {
            setCurrentChannel(undefined);
          }}
        />
        <ResetPassword
          open={openResetPassword}
          setOpen={setOpenResetPassword}
          channel={resetPasswordChannel.current}
        />
        <ChannelTableSearch
          onSearch={(values) => {
            setPagination({ pageIndex: 0, pageSize: 20 });
            setSearchParams(values);
          }}
        />
      </div>
      <TableData
        columns={columns as ColumnDef<Channel, unknown>[]}
        data={query.data?.data ?? []}
        rowCount={query.data?.total ?? 0}
        pagination={pagination}
        setPagination={setPagination}
      />
      {/* <TableBase
        params={searchParams}
        columns={columns as ColumnDef<Channel, unknown>[]}
      /> */}
    </div>
  );
}
