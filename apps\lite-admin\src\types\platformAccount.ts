export type PlatformAccountParams = {
  /**
   * 账号状态 0未登录 1成功 2过期失效 3失败
   */
  loginStatus?: number;
  /**
   * 根据媒体账号名称/备注名称模糊查询
   */
  platformAccountName?: string;
  /**
   * 平台查询
   */
  platformName?: string;
  /**
   * 按所属团队名称或者id查询
   */
  teamName?: string;

  /**
   * 创建时间结束时间
   */
  createEndTime?: number;
  /**
   * 创建时间开始时间
   */
  createStartTime?: number;
};

export interface PlatformAccount {
  /**
   * 云检测登录有效性时间戳
   */
  cloudCheckTime: number;
  /**
   * 创建时间戳
   */
  createdAt: number;
  /**
   * 在线天数
   */
  onLineDays: number;
  /**
   * 媒体账号名称
   */
  parentId: string;
  remark: string;
  platformAccountName: string;
  platformName: string;
  /**
   * 账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败, 4：取消授权>
   */
  status: number;
  teamCode: string;
  teamName: string;
  /**
   * 最后登录时间戳
   */
  updatedAt: number;
  /**
   * 账号数据云端同步时间
   */
  cloudUpdatedAt: number;
}
