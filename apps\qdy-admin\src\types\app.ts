export enum AppPlatformType {
  Ios = 'ios',
  Android = 'android',
  All = 'all',
}

export interface AppVersionParams {
  type?: AppPlatformType;
  /**
   * 结束日期时间戳
   */
  releaseTimeMax?: number;
  /**
   * 起始日期时间戳
   */
  releaseTimeMin?: number;
}

enum Type {
  Android = 'android',
  Ios = 'ios',
}

export interface AppVersion {
  _id: string;
  /**
   * 描述
   */
  desc: string;
  /**
   * 是否强制更新
   */
  force: boolean;
  /**
   * 类型
   */
  type: Type;
  /**
   * 下载地址
   */
  url: string;
  /**
   * 版本号
   */
  version: string;

  /**
   * 发布时间
   */
  releaseTime: number;

  /**
   * 发布者
   */
  user: {
    name: string;
    id: string;
  };
}

/**
 * PublicCreateUploadUrlResponseDTO
 */
export interface ICreateUploadUrlResponse {
  key: string;
  host: string;
  policy: string;
  'x-tos-algorithm': string;
  'x-tos-date': string;
  'x-tos-credential': string;
  'x-tos-signature': string;
}

export interface ICreateAppVersion {
  version: string;
  force: boolean;
  type: AppPlatformType;
  url: string;
  desc: string;
  file?: File;
}
